<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mixc.be.rsms.business.mapper.TbBusinessCommitmentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="mixc.be.rsms.business.domain.pojo.TbBusinessCommitment">
        <id column="id" property="id" />
        <result column="commitment_code" property="commitmentCode" />
        <result column="commitment_type" property="commitmentType" />
        <result column="amount" property="amount" />
        <result column="checkout_time" property="checkoutTime" />
        <result column="distribution_status" property="distributionStatus" />
        <result column="revision" property="revision" />
        <result column="create_user" property="createUser" />
        <result column="create_time" property="createTime" />
        <result column="update_user" property="updateUser" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, commitment_code, commitment_type, amount, checkout_time, distribution_status,
        revision, create_user, create_time, update_user, update_time, is_deleted
    </sql>

</mapper>
