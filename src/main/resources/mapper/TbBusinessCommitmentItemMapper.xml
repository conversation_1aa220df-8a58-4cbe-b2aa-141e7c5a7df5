<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mixc.be.rsms.business.mapper.TbBusinessCommitmentItemMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="mixc.be.rsms.business.domain.pojo.TbBusinessCommitmentItem">
        <id column="id" property="id" />
        <result column="commitment_id" property="commitmentId" />
        <result column="transaction_id" property="transactionId" />
        <result column="performance_type" property="performanceType" />
        <result column="business_type" property="businessType" />
        <result column="commitment_type" property="commitmentType" />
        <result column="commitment_amount" property="commitmentAmount" />
        <result column="revision" property="revision" />
        <result column="create_user" property="createUser" />
        <result column="create_time" property="createTime" />
        <result column="update_user" property="updateUser" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, commitment_id, transaction_id, performance_type, business_type, commitment_type, commitment_amount,
        revision, create_user, create_time, update_user, update_time, is_deleted
    </sql>

</mapper>
