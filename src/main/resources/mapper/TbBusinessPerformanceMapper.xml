<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mixc.be.rsms.business.mapper.TbBusinessPerformanceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="mixc.be.rsms.business.domain.pojo.TbBusinessPerformance">
        <id column="id" property="id" />
        <result column="transaction_id" property="transactionId" />
        <result column="performance_code" property="performanceCode" />
        <result column="user_id" property="userId" />
        <result column="store_id" property="storeId" />
        <result column="performance_period" property="performancePeriod" />
        <result column="performance_type" property="performanceType" />
        <result column="performance_stage" property="performanceStage" />
        <result column="amount" property="amount" />
        <result column="excluding_tax_amount" property="excludingTaxAmount" />
        <result column="performance_time" property="performanceTime" />
        <result column="revision" property="revision" />
        <result column="create_user" property="createUser" />
        <result column="create_time" property="createTime" />
        <result column="update_user" property="updateUser" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, transaction_id, performance_code, user_id, store_id, performance_period, performance_type, 
        performance_stage, amount, excluding_tax_amount, performance_time,
        revision, create_user, create_time, update_user, update_time, is_deleted
    </sql>

</mapper>
