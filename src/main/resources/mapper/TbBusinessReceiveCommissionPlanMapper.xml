<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mixc.be.rsms.business.mapper.TbBusinessReceiveCommissionPlanMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="mixc.be.rsms.business.domain.pojo.TbBusinessReceiveCommissionPlan">
        <id column="id" property="id" />
        <result column="report_id" property="reportId" />
        <result column="cost_item_id" property="costItemId" />
        <result column="cost_item_code" property="costItemCode" />
        <result column="cost_item_desc" property="costItemDesc" />
        <result column="payer" property="payer" />
        <result column="receivable_commission" property="receivableCommission" javaType="java.math.BigDecimal" jdbcType="DECIMAL"/>
        <result column="received_commission" property="receivedCommission" javaType="java.math.BigDecimal" jdbcType="DECIMAL"/>
        <result column="expect_payment" property="expectPayment" />
        <result column="actual_payment" property="actualPayment" />
        <result column="deposit_id" property="depositId" />
        <result column="remark" property="remark" />
        <result column="order_id" property="orderId" />
        <result column="order_code" property="orderCode" />
        <result column="order_status" property="orderStatus" />
        <result column="sub_order_id" property="subOrderId" />
        <result column="sub_order_code" property="subOrderCode" />
        <result column="revision" property="revision" />
        <result column="create_user" property="createUser" />
        <result column="create_time" property="createTime" />
        <result column="update_user" property="updateUser" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, report_id, cost_item_id, cost_item_code, cost_item_desc, payer, receivable_commission, received_commission, expect_payment, actual_payment, deposit_id, remark, order_id, order_code, order_status, sub_order_id, sub_order_code, revision, create_user,
        create_time, update_user, update_time, is_deleted
    </sql>

</mapper>
