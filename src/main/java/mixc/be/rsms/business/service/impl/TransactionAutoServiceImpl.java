package mixc.be.rsms.business.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mixc.be.rsms.business.domain.pojo.TbBusinessTransactionAuto;
import mixc.be.rsms.business.mapper.TbBusinessTransactionAutoMapper;
import mixc.be.rsms.business.service.ITransactionAutoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 成交报告-业绩分配信息-自动分配 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor_ = @__(@Autowired))
@RefreshScope
public class TransactionAutoServiceImpl extends ServiceImpl<TbBusinessTransactionAutoMapper, TbBusinessTransactionAuto> implements ITransactionAutoService {

}
