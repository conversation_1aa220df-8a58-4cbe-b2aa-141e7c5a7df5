package mixc.be.rsms.business.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mixc.be.rsms.business.domain.pojo.TbBusinessPerformanceItem;
import mixc.be.rsms.business.mapper.TbBusinessPerformanceItemMapper;
import mixc.be.rsms.business.service.IPerformanceItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 业绩主表-明细行 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor_ = @__(@Autowired))
@RefreshScope
public class PerformanceItemServiceImpl extends ServiceImpl<TbBusinessPerformanceItemMapper, TbBusinessPerformanceItem> implements IPerformanceItemService {

}
