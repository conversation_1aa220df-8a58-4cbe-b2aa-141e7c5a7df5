package mixc.be.rsms.business.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mixc.be.rsms.business.common.enums.BusinessEnum;
import mixc.be.rsms.business.common.exception.BusinessException;
import mixc.be.rsms.business.common.feignclient.AuthorServerClient;
import mixc.be.rsms.business.common.feignclient.DataServerClient;
import mixc.be.rsms.business.common.feignclient.MobileServerClient;
import mixc.be.rsms.business.common.feignclient.ThirdServerClient;
import mixc.be.rsms.business.common.mq.SendMsg;
import mixc.be.rsms.business.convert.*;
import mixc.be.rsms.business.domain.dto.*;
import mixc.be.rsms.business.domain.pojo.*;
import mixc.be.rsms.business.mapper.*;
import mixc.be.rsms.business.service.IOrderService;
import mixc.be.rsms.business.service.ITradeOrderLogService;
import mixc.be.rsms.business.utils.PermissionUtil;
import mixc.be.rsms.business.utils.ThirdPayUtil;
import mixc.be.rsms.business.vo.*;
import mixc.be.rsms.common.constant.RedisConstant;
import mixc.be.rsms.common.constant.RsmsConstant;
import mixc.be.rsms.common.enums.*;
import mixc.be.rsms.common.utils.AuthUtil;
import mixc.be.rsms.common.utils.FlowNumberUtil;
import mixc.be.rsms.common.utils.JacksonUtil;
import mixc.be.rsms.common.utils.SnowFlake;
import mixc.be.rsms.pojo.author.UserDetailsMicroResp;
import mixc.be.rsms.pojo.business.*;
import mixc.be.rsms.pojo.data.*;
import mixc.be.rsms.pojo.third.*;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @description
 * @date 2024/12/11
 */

@Slf4j
@Service
@RefreshScope
@RequiredArgsConstructor(onConstructor_ = @__(@Autowired))
public class OrderServiceImpl implements IOrderService {
    private final SnowFlake snowFlake;

    @Value("${sf.min_transfer_amount}")
    private BigDecimal minTransferAmount;

    private final TbBusinessOrderMapper orderMapper;
    private final TbBusinessSubOrderRefundMapper refundMapper;
    private final TbBusinessSubOrderReceiveMapper receiveMapper;
    private final TbBusinessSubOrderCommissionMapper commissionMapper;
    private final TbBusinessOrderCallbackMapper callbackMapper;

    private final DataOrderConvert orderConvert;
    private final DataSubOrderReceiveConvert receiveConvert;
    private final DataSubOrderRefundConvert refundConvert;
    private final DataSubOrderCommissionConvert commissionConvert;
    private final DataHouseConvert houseConvert;
    private final DataParkingSpaceConvert parkingSpaceConvert;

    private final TbBusinessDepositMapper depositMapper;
    private final TbBusinessTransactionInfoMapper transactionInfoMapper;
    private final TbBusinessTransactionReportMapper reportMapper;
    private final TbBusinessReceiveCommissionPlanMapper receiveCommissionPlanMapper;
    private final TbBusinessContractMapper contractMapper;
    private final TbBusinessContractSignMappingMapper signMappingMapper;
    private final TbBusinessContractMapper bizContractMapper;

    private final SendMsg sendMsg;

    private final DataServerClient dataServerClient;
    private final AuthorServerClient authorServerClient;
    private final FlowNumberUtil flowNumberUtil;
    private final StringRedisTemplate stringRedisTemplate;
    private final ThirdServerClient thirdServerClient;
    private final MobileServerClient mobileServerClient;
    private final ITradeOrderLogService tradeOrderLogService;

    @Value("${order.publicIP}")
    private String publicIp;

    @Value("${sf.source}")
    private String sfSource;

    /**
     * 商户微信公众号appid
     */
    @Value("${sf.wx_subAppId}")
    private String sfWxSubAppId;

    @Value("${spring.profiles.active}")
    private String active;

    @Value("${order.timeOut}")
    private Integer orderTimeOut;

    private final RedissonClient redissonClient;

    public static Map<String, String> tradeSceneMap = Map.of(
            DropdownEnum.PAYMENT_ORDER_TYPE_WXSMALLPROGRAM.getDictKey(), "WxJsApi",
            DropdownEnum.PAYMENT_ORDER_TYPE_WXH5.getDictKey(), "WxApp",
            DropdownEnum.PAYMENT_ORDER_TYPE_ALIH5.getDictKey(), "AliNative",
            DropdownEnum.PAYMENT_ORDER_TYPE_ALISMALLPROGRAM.getDictKey(), "AliNative"
//                ,
//                DropdownEnum.TRADE_SCENE_ALI_JSAPI.getDictKey(), "AliJsApi"
    );


    @Override
    public IPage<OrderVo> page(OrderQueryParams orderQueryParams) {
        if (ObjectUtils.isEmpty(orderQueryParams)) {
            return new Page<>(1, 10, 0);
        }
        List<Long> communityIdByNameKeyWord = new LinkedList<>();
        if (!ObjectUtils.isEmpty(orderQueryParams.getCommunityKeyWord())) {
            communityIdByNameKeyWord.addAll(dataServerClient.queryCommunityIdByNameKeyWord(orderQueryParams.getCommunityKeyWord()));
            if (ObjectUtils.isEmpty(communityIdByNameKeyWord)) {
                return new Page<>(orderQueryParams.getPageNum(), orderQueryParams.getPageSize(), 0);
            }
        }
        List<Long> brokerIds = new LinkedList<>();
        if (!ObjectUtils.isEmpty(orderQueryParams.getAgentOrCreateBy())) {
            brokerIds.addAll(authorServerClient.getUserIdsByUserName(orderQueryParams.getAgentOrCreateBy()));
            if (ObjectUtils.isEmpty(brokerIds)) {
                Page<OrderVo> orderVoPage = new Page<>(orderQueryParams.getPageNum(), orderQueryParams.getPageSize(), 0);
                orderVoPage.setRecords(List.of());
                return orderVoPage;
            }
        }

        List<Long> orderIdList = new LinkedList<>();
        if (!ObjectUtils.isEmpty(orderQueryParams.getDocumentNumKeyWord())) {
            orderIdList.addAll(depositMapper.selectList(Wrappers.lambdaQuery(TbBusinessDeposit.class)
                            .like(TbBusinessDeposit::getDepositCode, orderQueryParams.getDocumentNumKeyWord()))
                    .stream().map(TbBusinessDeposit::getOrderId)
                    .toList());
            List<Long> reportIdList = reportMapper.selectList(Wrappers.lambdaQuery(TbBusinessTransactionReport.class)
                            .like(TbBusinessTransactionReport::getReportCode, orderQueryParams.getDocumentNumKeyWord()))
                    .stream()
                    .map(TbBusinessTransactionReport::getId)
                    .toList();
            if (!ObjectUtils.isEmpty(reportIdList)) {
                orderIdList.addAll(receiveCommissionPlanMapper.selectList(Wrappers.lambdaQuery(TbBusinessReceiveCommissionPlan.class)
                                .in(TbBusinessReceiveCommissionPlan::getReportId, reportIdList))
                        .stream()
                        .map(TbBusinessReceiveCommissionPlan::getOrderId)
                        .toList());
            }
            if (ObjectUtils.isEmpty(orderIdList)) {
                return new Page<>(orderQueryParams.getPageNum(), orderQueryParams.getPageSize(), 0);
            }
        }
        // 由于车位模块现在暂未实现，故这里只查询房源ID
        List<Long> businessIdList = new LinkedList<>();
        if (!ObjectUtils.isEmpty(orderQueryParams.getHouseOrParkingKeyWord())) {
            businessIdList.addAll(dataServerClient.getHouseIdByCommunityKeyWord(orderQueryParams.getHouseOrParkingKeyWord()));
            if (ObjectUtils.isEmpty(businessIdList)) {
                return new Page<>(orderQueryParams.getPageNum(), orderQueryParams.getPageSize(), 0);
            }
        }
        // 权限
        List<String> permissionList = List.of(
                PermissionEnum.ORDER_MANAGER_ORDER_VIEW.getCode()
        );
        log.info("权限码 list = {}", permissionList);
        List<Long> permissionUserIdList = PermissionUtil.getUserIdListByPermissionCode(permissionList)
                .get(PermissionEnum.ORDER_MANAGER_ORDER_VIEW.getCode());
        log.info("权限码对应用户Id Map = {}", permissionUserIdList);
        if (ObjectUtils.isEmpty(permissionUserIdList) || permissionUserIdList.contains(PermissionIsAllValueEnum.DISABLE.getValue())) {
            throw new BusinessException(CommonEnum.NOT_PERMISSION_ERROR);
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String orderDateFrom = "", orderDateTo = "";
        if (!ObjectUtils.isEmpty(orderQueryParams.getOrderDateTo())
                && !ObjectUtils.isEmpty(orderQueryParams.getOrderDateFrom())) {
            orderDateFrom = orderQueryParams.getOrderDateFrom().format(formatter) + " 00:00:00";
            orderDateTo = orderQueryParams.getOrderDateTo().format(formatter) + " 23:59:59";
        }
        QueryWrapper<TbBusinessOrder> eq = Wrappers.query(TbBusinessOrder.class)
                .like(!ObjectUtils.isEmpty(orderQueryParams.getOrderCode()), "order_code", orderQueryParams.getOrderCode())
                .eq(!ObjectUtils.isEmpty(orderQueryParams.getStatus()), "status", orderQueryParams.getStatus())
                .eq(!ObjectUtils.isEmpty(orderQueryParams.getTransactionType()), "transaction_type", orderQueryParams.getTransactionType())
                .like(!ObjectUtils.isEmpty(orderQueryParams.getCustomerNameKeyWord()), "customer_name", orderQueryParams.getCustomerNameKeyWord())
                .between(!ObjectUtils.isEmpty(orderDateFrom) && !ObjectUtils.isEmpty(orderDateTo),
                        "order_date", orderDateFrom, orderDateTo)
                .like(!ObjectUtils.isEmpty(orderQueryParams.getCustomerMobileKeyWord()), "customer_mobile", orderQueryParams.getCustomerMobileKeyWord())
                .in(!ObjectUtils.isEmpty(brokerIds), "create_user", brokerIds)
                .in(!ObjectUtils.isEmpty(orderIdList), "id", orderIdList)
                .nested(!ObjectUtils.isEmpty(businessIdList), i -> {
                    businessIdList.forEach(id -> {
                        i.like("house_ids", id)
                                .or()
                                .like("parking_ids", id)
                                .or();
                    });
                    i.eq("id", 0);
                })
                .nested(!ObjectUtils.isEmpty(communityIdByNameKeyWord), i -> {
                    communityIdByNameKeyWord.forEach(id -> {
                        i.like("community_ids", id)
                                .or()
                                .eq("new_house_community_id", id)
                                .or();
                    });
                    i.eq("id", 0);
                });

        if (!permissionUserIdList.contains(PermissionIsAllValueEnum.ALL.getValue())) {
            eq.nested(i -> {
                i.in("create_user", permissionUserIdList);
            });
        }
        eq.orderByAsc("case status " +
                " when 'unpay' then 0 " +
                " when 'part_pay' then 1 " +
                " when 'payed' then 2 " +
                " else 3 end");
        eq.orderByDesc("create_time");
        Page<TbBusinessOrder> tbBusinessOrderPage = orderMapper.selectPage(new Page<>(orderQueryParams.getPageNum(), orderQueryParams.getPageSize()), eq);

        Map<Long, String> orderDocumentNum = getOrderDocumentNum(tbBusinessOrderPage.getRecords().stream().map(TbBusinessOrder::getId).toList());
        // 房源信息查询
        List<Long> houseIdList = tbBusinessOrderPage.getRecords()
                .stream()
                .map(TbBusinessOrder::getHouseIds)
                .filter(Objects::nonNull)
                .map(i -> Arrays.stream(i.split(RsmsConstant.SPLIT_COMMA)).toList())
                .flatMap(Collection::stream)
                .map(Long::valueOf)
                .distinct()
                .toList();
        Map<Long, HouseInfoVo> houseInfoVoMap = dataServerClient.listHouseInfoByIds(houseIdList)
                .stream()
                .collect(Collectors.toMap(HouseInfoVo::getId, Function.identity(), (existing, next) -> existing));

        // 车位信息查询
        List<Long> parkingSpaceIdList = tbBusinessOrderPage.getRecords()
                .stream()
                .map(TbBusinessOrder::getParkingIds)
                .filter(Objects::nonNull)
                .map(i -> Arrays.stream(i.split(RsmsConstant.SPLIT_COMMA)).toList())
                .flatMap(Collection::stream)
                .map(Long::valueOf)
                .distinct()
                .toList();
        Map<Long, ParkingSpaceVo> parkingSpaceVoMap = dataServerClient.getParkingSpaceByIdList(parkingSpaceIdList)
                .stream()
                .map(parkingSpaceConvert::resp2Vo)
                .collect(Collectors.toMap(ParkingSpaceVo::getId, Function.identity(), (existing, next) -> existing));

        // 新房项目名
        List<Long> newHouseCommunityIdList = tbBusinessOrderPage.getRecords()
                .stream()
                .map(TbBusinessOrder::getNewHouseCommunityId)
                .filter(Objects::nonNull)
                .distinct()
                .toList();
        Map<Long, NewHouseResp> newHouseRespMap = dataServerClient.getNewHouseByIdList(newHouseCommunityIdList).stream()
                .collect(Collectors.toMap(NewHouseResp::getId, Function.identity(), (existing, next) -> existing));

        List<OrderVo> list = tbBusinessOrderPage.getRecords()
                .stream()
                .map(i -> {
                    OrderVo orderVo = orderConvert.data2Vo(i);
                    // 设置房源信息
                    if (!ObjectUtils.isEmpty(i.getHouseIds())) {
                        orderVo.setHouseInfo(Arrays.stream(i.getHouseIds().split(RsmsConstant.SPLIT_COMMA))
                                .map(Long::valueOf)
                                .map(houseInfoVoMap::get)
                                .filter(Objects::nonNull)
                                .toList());
                        orderVo.setBusinessType(DropdownEnum.ORDER_BUSINESS_HOUSE.getDictKey());
                    } else if (!ObjectUtils.isEmpty(i.getParkingIds())) {
                        // 设置车位信息
                        orderVo.setParking(Arrays.stream(i.getParkingIds().split(RsmsConstant.SPLIT_COMMA))
                                .map(Long::valueOf)
                                .map(parkingSpaceVoMap::get)
                                .filter(Objects::nonNull)
                                .toList());
                        orderVo.setBusinessType(DropdownEnum.ORDER_BUSINESS_PARKING_SPACE.getDictKey());
                    } else if (!ObjectUtils.isEmpty(i.getNewHouseCommunityId())) {
                        NewHouseCommunityVo newHouseCommunityVo = new NewHouseCommunityVo();
                        newHouseCommunityVo.setNewHouseCommunityId(i.getNewHouseCommunityId());
                        newHouseCommunityVo.setBuilding(i.getBuilding());
                        newHouseCommunityVo.setUnit(i.getUnit());
                        newHouseCommunityVo.setHouseNumber(i.getHouseNumber());
                        if (!ObjectUtils.isEmpty(newHouseRespMap.get(i.getNewHouseCommunityId()))) {
                            NewHouseResp newHouseResp = newHouseRespMap.get(i.getNewHouseCommunityId());
                            newHouseCommunityVo.setNewHouseCommunityName(newHouseResp.getCommunityName());
                        }
                        orderVo.setNewHouseCommunity(newHouseCommunityVo);
                        orderVo.setBusinessType(DropdownEnum.ORDER_BUSINESS_NEW_HOUSE_COMMUNITY.getDictKey());
                    }
                    return orderVo;
                })
                .peek(orderVo -> orderVo.setDocumentNum(orderDocumentNum.get(orderVo.getId()))).toList();

        List<Long> createyByIdList = list
                .stream()
                .map(OrderVo::getCreateUser)
                .filter(Objects::nonNull)
                .toList();
        if (!ObjectUtils.isEmpty(createyByIdList)) {
            Map<Long, UserDetailsMicroResp> createByMap = authorServerClient.getUserDetailsMicro(createyByIdList)
                    .stream()
                    .collect(Collectors.toMap(UserDetailsMicroResp::getId, Function.identity(), (existing, next) -> existing));
            list.forEach(i -> {
                if (!ObjectUtils.isEmpty(createByMap.get(i.getCreateUser()))) {
                    UserDetailsMicroResp userDetailsMicroResp = createByMap.get(i.getCreateUser());
                    i.setCreateName(userDetailsMicroResp.getEmpName());
                    i.setCreateMobile(userDetailsMicroResp.getMobile());
                }
            });
        }

        Page<OrderVo> result = new Page<>(tbBusinessOrderPage.getCurrent(), tbBusinessOrderPage.getSize(), tbBusinessOrderPage.getTotal());
        result.setRecords(list);
        return result;
    }

    /**
     * 根据 订单ID 查询对应的 意向金编号、成交报告编号
     *
     * @param orderIdList
     * @return
     */
    private Map<Long, String> getOrderDocumentNum(List<Long> orderIdList) {
        if (ObjectUtils.isEmpty(orderIdList)) {
            return Map.of();
        }
        Map<Long, String> resultMap = depositMapper.selectList(Wrappers.lambdaQuery(TbBusinessDeposit.class)
                        .in(TbBusinessDeposit::getOrderId, orderIdList))
                .stream()
                .collect(Collectors.toMap(TbBusinessDeposit::getOrderId, TbBusinessDeposit::getDepositCode));
        Map<Long, Long> reportIdMap = receiveCommissionPlanMapper.selectList(Wrappers.lambdaQuery(TbBusinessReceiveCommissionPlan.class)
                        .in(TbBusinessReceiveCommissionPlan::getOrderId, orderIdList))
                .stream()
                .collect(Collectors.toMap(TbBusinessReceiveCommissionPlan::getOrderId, TbBusinessReceiveCommissionPlan::getReportId, (existing, next) -> existing));
        if (ObjectUtils.isEmpty(reportIdMap.values())) {
            return resultMap;
        }
        Map<Long, String> reportCodeMap = reportMapper.selectList(Wrappers.lambdaQuery(TbBusinessTransactionReport.class)
                        .in(TbBusinessTransactionReport::getId, reportIdMap.values()))
                .stream()
                .collect(Collectors.toMap(TbBusinessTransactionReport::getId, TbBusinessTransactionReport::getReportCode, (existing, next) -> existing));

        resultMap.putAll(orderIdList.stream()
                .filter(orderId -> !ObjectUtils.isEmpty(reportIdMap.get(orderId)))
                .filter(orderId -> !ObjectUtils.isEmpty(reportCodeMap.get(reportIdMap.get(orderId))))
                .collect(Collectors.toMap(orderId -> orderId, orderId -> reportCodeMap.get(reportIdMap.get(orderId)), (existing, next) -> existing)));
        return resultMap;
    }

    @Override
    public OrderVo detail(Long id) {
        TbBusinessOrder tbBusinessOrder = orderMapper.selectById(id);
        if (tbBusinessOrder == null) {
            return null;
        }
        OrderVo orderVo = orderConvert.data2Vo(tbBusinessOrder);
        getSubOrder(orderVo);

        // 设置房源车位信息
        if (!ObjectUtils.isEmpty(tbBusinessOrder.getHouseIds())) {
            List<Long> houseIdList = Arrays.stream(tbBusinessOrder.getHouseIds().split(RsmsConstant.SPLIT_COMMA))
                    .map(Long::valueOf)
                    .distinct()
                    .toList();
            orderVo.setHouseInfo(dataServerClient.listHouseInfoByIds(houseIdList));
            orderVo.setBusinessType(DropdownEnum.ORDER_BUSINESS_HOUSE.getDictKey());
        }else if(!ObjectUtils.isEmpty(tbBusinessOrder.getParkingIds())) {
            List<Long> parkingSpaceIdList = Arrays.stream(tbBusinessOrder.getParkingIds().split(RsmsConstant.SPLIT_COMMA))
                    .map(Long::valueOf)
                    .distinct()
                    .toList();
            orderVo.setParking(dataServerClient.getParkingSpaceByIdList(parkingSpaceIdList)
                    .stream()
                    .map(parkingSpaceConvert::resp2Vo)
                    .toList());
            orderVo.setBusinessType(DropdownEnum.ORDER_BUSINESS_PARKING_SPACE.getDictKey());
        }else if (!ObjectUtils.isEmpty(tbBusinessOrder.getNewHouseCommunityId())) {
            NewHouseResp newHouseResp = dataServerClient.getNewHouseByIdList(List.of(tbBusinessOrder.getNewHouseCommunityId()))
                    .stream()
                    .collect(Collectors.toMap(NewHouseResp::getId, Function.identity(), (existing, next) -> existing))
                    .get(tbBusinessOrder.getNewHouseCommunityId());
            NewHouseCommunityVo newHouseCommunityVo = new NewHouseCommunityVo();
            newHouseCommunityVo.setNewHouseCommunityId(tbBusinessOrder.getNewHouseCommunityId());
            newHouseCommunityVo.setBuilding(tbBusinessOrder.getBuilding());
            newHouseCommunityVo.setUnit(tbBusinessOrder.getUnit());
            newHouseCommunityVo.setHouseNumber(tbBusinessOrder.getHouseNumber());
            if (!ObjectUtils.isEmpty(newHouseResp)) {
                newHouseCommunityVo.setNewHouseCommunityName(newHouseResp.getCommunityName());
            }
            orderVo.setNewHouseCommunity(newHouseCommunityVo);
            orderVo.setBusinessType(DropdownEnum.ORDER_BUSINESS_NEW_HOUSE_COMMUNITY.getDictKey());
        }

        // 填充创建人信息
        UserDetailsMicroResp userDetailsMicroResp = authorServerClient.getUserDetailsMicro(List.of(orderVo.getCreateUser()))
                .stream()
                .collect(Collectors.toMap(UserDetailsMicroResp::getId, Function.identity(), (existing, next) -> existing))
                .get(orderVo.getCreateUser());
        if (!ObjectUtils.isEmpty(userDetailsMicroResp)) {
            orderVo.setCreateName(userDetailsMicroResp.getEmpName());
            orderVo.setCreateMobile(userDetailsMicroResp.getMobile());
        }
        String documentNum = getOrderDocumentNum(List.of(orderVo.getId()))
                .get(orderVo.getId());
        orderVo.setDocumentNum(documentNum);
        return orderVo;
    }

    /**
     * 填充子订单
     * @param orderVo
     */
    private void getSubOrder(OrderVo orderVo) {
        List<Long> userIdList = new LinkedList<>();

        Long id = orderVo.getId();

        List<TbBusinessSubOrderReceive> orderReceives = receiveMapper.selectList(Wrappers.lambdaQuery(TbBusinessSubOrderReceive.class)
                .eq(TbBusinessSubOrderReceive::getParentOrderId, id)
                .orderByDesc(TbBusinessSubOrderReceive::getCreateTime));
        List<TbBusinessSubOrderRefund> orderRefunds = refundMapper.selectList(Wrappers.lambdaQuery(TbBusinessSubOrderRefund.class)
                .eq(TbBusinessSubOrderRefund::getParentOrderId, id)
                .orderByDesc(TbBusinessSubOrderRefund::getCreateTime));
        List<TbBusinessSubOrderCommission> commissions = commissionMapper.selectList(Wrappers.lambdaQuery(TbBusinessSubOrderCommission.class)
                .eq(TbBusinessSubOrderCommission::getParentOrderId, id)
                .orderByDesc(TbBusinessSubOrderCommission::getCreateTime));

        Map<Long, TbBusinessSubOrderReceive> receiveMap = new HashMap<>();
        if (!ObjectUtils.isEmpty(orderReceives)) {
            userIdList.addAll(orderReceives.stream()
                    .map(TbBusinessSubOrderReceive::getCreateUser)
                    .filter(Objects::nonNull)
                    .toList());
            receiveMap.putAll(orderReceives.stream().collect(Collectors.toMap(TbBusinessSubOrderReceive::getId, Function.identity())));
        }
        if (!ObjectUtils.isEmpty(orderRefunds)) {
            userIdList.addAll(orderRefunds.stream()
                    .map(TbBusinessSubOrderRefund::getCreateUser)
                    .filter(Objects::nonNull)
                    .toList());
        }
        if (!ObjectUtils.isEmpty(commissions)) {
            userIdList.addAll(commissions.stream()
                    .map(TbBusinessSubOrderCommission::getCreateUser)
                    .filter(Objects::nonNull)
                    .toList());
        }
        Map<Long, UserDetailsMicroResp> userDetailsMicroMap = new HashMap<>();
        if (!ObjectUtils.isEmpty(userIdList)) {
            userDetailsMicroMap.putAll(authorServerClient.getUserDetailsMicro(userIdList)
                    .stream()
                    .collect(Collectors.toMap(UserDetailsMicroResp::getId, Function.identity(), (existing, next) -> existing)));
        }

        List<OrderReceiveVo> receiveList = orderReceives.stream()
                .map(orderReceive -> {
                    OrderReceiveVo orderReceiveVo = receiveConvert.data2Vo(orderReceive);
                    UserDetailsMicroResp userDetailsMicroResp = userDetailsMicroMap.get(orderReceiveVo.getCreateUser());
                    if (!ObjectUtils.isEmpty(userDetailsMicroResp)) {
                        orderReceiveVo.setCreateName(userDetailsMicroResp.getEmpName());
                        orderReceiveVo.setCreateMobile(userDetailsMicroResp.getMobile());
                    }
                    return orderReceiveVo;
                })
                .toList();
        orderVo.setReceiveOrder(receiveList);

        List<OrderRefundVo> refundVoList = orderRefunds.stream()
                .map(orderRefund -> {
                    OrderRefundVo orderRefundVo = refundConvert.data2Vo(orderRefund);
                    TbBusinessSubOrderReceive orderReceive = receiveMap.get(orderRefund.getReceiveSubOrderId());
                    if (!ObjectUtils.isEmpty(orderReceive)) {
                        SubItemRefundOrderVo subItemRefundOrderVo = new SubItemRefundOrderVo();
                        subItemRefundOrderVo.setReceiveSubOrderCode(orderReceive.getSubOrderCode());
                        subItemRefundOrderVo.setAmount(orderRefund.getAmount());
                        orderRefundVo.setSubItemOrderList(List.of(
                                subItemRefundOrderVo
                        ));
                    }
                    UserDetailsMicroResp userDetailsMicroResp = userDetailsMicroMap.get(orderRefundVo.getCreateUser());
                    if (!ObjectUtils.isEmpty(userDetailsMicroResp)) {
                        orderRefundVo.setCreateName(userDetailsMicroResp.getEmpName());
                        orderRefundVo.setCreateMobile(userDetailsMicroResp.getMobile());
                    }
                    return orderRefundVo;
                })
                .toList();
        orderVo.setRefundOrder(refundVoList);

        // 组装转佣数据
        if (!ObjectUtils.isEmpty(commissions)) {
            Map<Long, Long> subOrderAndParentOrderMap = receiveMapper.selectBatchIds(commissions.stream()
                            .map(TbBusinessSubOrderCommission::getExportSubOrderId)
                            .toList())
                    .stream()
                    .collect(Collectors.toMap(TbBusinessSubOrderReceive::getId, TbBusinessSubOrderReceive::getParentOrderId, (existing, next) -> existing));
            Map<Long, String> orderIdAndCodeMap = orderMapper.selectBatchIds(subOrderAndParentOrderMap.values())
                    .stream()
                    .collect(Collectors.toMap(TbBusinessOrder::getId, TbBusinessOrder::getOrderCode, (existing, next) -> existing));


            OrderCommissionVo orderCommissionVo = new OrderCommissionVo();
            orderCommissionVo.setParentOrderId(orderVo.getId());
            commissions.stream()
                    .map(TbBusinessSubOrderCommission::getSubOrderCode)
                    .findAny()
                    .ifPresent(orderCommissionVo::setSubOrderCode);
            commissions.stream().map(TbBusinessSubOrderCommission::getExportSubOrderId).findAny()
                    .ifPresent(i -> {
                        if (!ObjectUtils.isEmpty(subOrderAndParentOrderMap.get(i))) {
                            if (!ObjectUtils.isEmpty(orderIdAndCodeMap.get(subOrderAndParentOrderMap.get(i)))) {
                                orderCommissionVo.setExportOrderCode(orderIdAndCodeMap.get(subOrderAndParentOrderMap.get(i)));
                            }
                        }
                    });
            commissions.stream().map(TbBusinessSubOrderCommission::getExportAmount)
                    .reduce(BigDecimal::add)
                    .ifPresent(orderCommissionVo::setExportAmount);
            orderCommissionVo.setExportCommissionSubOrderVos(commissions.stream()
                    .map(i -> {
                        CommissionSubOrderVo commissionSubOrderVo = new CommissionSubOrderVo();
                        commissionSubOrderVo.setId(i.getId());
                        commissionSubOrderVo.setSubOrderCode(i.getExportSubOrderCode());
                        commissionSubOrderVo.setAmount(i.getExportAmount());
                        return commissionSubOrderVo;
                    }).toList());
            orderCommissionVo.setImportAmount(orderCommissionVo.getExportAmount());
            commissions.stream()
                    .map(TbBusinessSubOrderCommission::getOrderStatus)
                    .findAny()
                    .ifPresent(orderCommissionVo::setOrderStatus);
            CommissionSubOrderVo importSub = new CommissionSubOrderVo();
            commissions.stream()
                    .findAny()
                    .ifPresent(i -> {
                        importSub.setSubOrderCode(i.getSubOrderCode());
                    });
            importSub.setAmount(orderCommissionVo.getExportAmount());
            orderCommissionVo.setImportCommissionSubOrderVos(List.of(importSub));
            commissions.stream()
                    .map(TbBusinessSubOrderCommission::getCreateTime)
                    .findAny()
                    .ifPresent(orderCommissionVo::setCreateTime);
            commissions.stream()
                    .map(TbBusinessSubOrderCommission::getBusinessId)
                    .filter(Objects::nonNull)
                    .findAny()
                    .ifPresent(orderCommissionVo::setBusinessId);
            orderCommissionVo.setImportOrderCode(orderVo.getOrderCode());
            commissions.stream()
                    .findAny()
                    .ifPresent(commission -> {
                        UserDetailsMicroResp userDetailsMicroResp = userDetailsMicroMap.get(commission.getCreateUser());
                        if (!ObjectUtils.isEmpty(userDetailsMicroResp)) {
                            orderCommissionVo.setCreateName(userDetailsMicroResp.getEmpName());
                            orderCommissionVo.setCreateMobile(userDetailsMicroResp.getMobile());
                        }
                    });
            orderVo.setCommissionOrder(List.of(orderCommissionVo));
        }else {
            orderVo.setCommissionOrder(List.of());
        }
    }

    @Override
    public List<OrderResp> queryOrderByContractNum(List<String> contractNums, String customerMobile) {
        if (ObjectUtils.isEmpty(contractNums)) {
            return List.of();
        }
        LambdaQueryWrapper<TbBusinessOrder> wrapper = Wrappers.lambdaQuery(TbBusinessOrder.class);
        wrapper.eq(!ObjectUtils.isEmpty(customerMobile), TbBusinessOrder::getCustomerMobile, customerMobile);
        wrapper.nested(!ObjectUtils.isEmpty(contractNums), i -> {
            contractNums.forEach(c -> {
                i.like(TbBusinessOrder::getContractNum, c)
                        .or();
            });
            i.eq(TbBusinessOrder::getId, 0);
        });
        List<TbBusinessOrder> orders = orderMapper.selectList(wrapper);

        // 设置房源车位信息
        List<Long> houseIdList = orders.stream()
                .map(TbBusinessOrder::getHouseIds)
                .filter(Objects::nonNull)
                .map(houseIds -> Arrays.stream(houseIds.split(RsmsConstant.SPLIT_COMMA)).toList())
                .flatMap(Collection::stream)
                .map(Long::valueOf)
                .distinct()
                .toList();
        Map<Long, HouseInfoVo> houseInfoVoMap = queryHouseInfoMap(houseIdList);

        List<Long> parkingSpaceIdList = orders.stream()
                .map(TbBusinessOrder::getParkingIds)
                .filter(Objects::nonNull)
                .map(parkingSpaceIds -> Arrays.stream(parkingSpaceIds.split(RsmsConstant.SPLIT_COMMA)).toList())
                .flatMap(Collection::stream)
                .map(Long::valueOf)
                .distinct()
                .toList();
        Map<Long, ParkingSpaceResp> parkingSpaceRespMap = queryParkingSpaceInfoMap(parkingSpaceIdList);

        List<OrderResp> orderResps = orders
                .stream()
                .map(order -> {
                    return generateOrderResp(order, houseInfoVoMap, parkingSpaceRespMap);
                })
                .toList();
        setSubOrderDetail(orderResps);
        return orderResps;
    }

    private Map<Long, HouseInfoVo> queryHouseInfoMap(List<Long> houseIdList) {
        if (ObjectUtils.isEmpty(houseIdList)) {
            return Map.of();
        }
        return dataServerClient.listHouseInfoByIds(houseIdList)
                .stream()
                .collect(Collectors.toMap(HouseInfoVo::getId, Function.identity(), (existing, next) -> existing));
    }

    private Map<Long, ParkingSpaceResp> queryParkingSpaceInfoMap(List<Long> parkingSpaceIdList) {
        if (ObjectUtils.isEmpty(parkingSpaceIdList)) {
            return Map.of();
        }
        return dataServerClient.getParkingSpaceByIdList(parkingSpaceIdList)
                .stream()
                .collect(Collectors.toMap(ParkingSpaceResp::getId, Function.identity(), (existing, next) -> existing));
    }

    private OrderResp generateOrderResp(TbBusinessOrder order, Map<Long, HouseInfoVo> houseInfoVoMap, Map<Long, ParkingSpaceResp> parkingSpaceRespMap) {
        OrderResp orderResp = orderConvert.data2Resp(order);
        if (!ObjectUtils.isEmpty(order.getHouseIds())) {
            List<HouseInfoResp> houseInfoResps = Arrays.stream(order.getHouseIds().split(RsmsConstant.SPLIT_COMMA))
                    .map(Long::valueOf)
                    .map(houseId -> {
                        if (!ObjectUtils.isEmpty(houseInfoVoMap.get(houseId))) {
                            return houseConvert.vo2Resp(houseInfoVoMap.get(houseId));
                        }
                        return null;
                    })
                    .filter(Objects::nonNull)
                    .toList();
            orderResp.setHouseInfo(houseInfoResps);
            orderResp.setBusinessType(DropdownEnum.ORDER_BUSINESS_HOUSE.getDictKey());
        } else if (!ObjectUtils.isEmpty(order.getParkingIds())) {
            List<ParkingSpaceResp> parkingSpaceRespList = Arrays.stream(order.getParkingIds().split(RsmsConstant.SPLIT_COMMA))
                    .map(Long::valueOf)
                    .map(parkingSpaceId -> {
                        if (!ObjectUtils.isEmpty(parkingSpaceRespMap.get(parkingSpaceId))) {
                            return parkingSpaceRespMap.get(parkingSpaceId);
                        }
                        return null;
                    })
                    .filter(Objects::nonNull)
                    .toList();
            orderResp.setParking(parkingSpaceRespList);
            orderResp.setBusinessType(DropdownEnum.ORDER_BUSINESS_PARKING_SPACE.getDictKey());
        } else if (!ObjectUtils.isEmpty(order.getNewHouseCommunityId())) {
            NewHouseResp newHouseResp = dataServerClient.getNewHouseByIdList(List.of(order.getNewHouseCommunityId()))
                    .stream()
                    .collect(Collectors.toMap(NewHouseResp::getId, Function.identity(), (existing, next) -> existing))
                    .get(order.getNewHouseCommunityId());
            NewHouseCommunityResp newHouseCommunityResp = new NewHouseCommunityResp();
            newHouseCommunityResp.setNewHouseCommunityId(order.getNewHouseCommunityId());
            newHouseCommunityResp.setBuilding(order.getBuilding());
            newHouseCommunityResp.setUnit(order.getUnit());
            newHouseCommunityResp.setHouseNumber(order.getHouseNumber());
            if (!ObjectUtils.isEmpty(newHouseResp)) {
                newHouseCommunityResp.setNewHouseCommunityName(newHouseResp.getCommunityName());
            }
            orderResp.setNewHouseCommunity(newHouseCommunityResp);
            orderResp.setBusinessType(DropdownEnum.ORDER_BUSINESS_NEW_HOUSE_COMMUNITY.getDictKey());
        }
        return orderResp;
    }

    @Override
    public Page<OrderResp> queryOrderByCustomerMobile(Integer pageNum, Integer pageSize, String customerMobile) {
        if (ObjectUtils.isEmpty(customerMobile)) {
            return new Page<>(1, pageSize, 0);
        }
        IPage<TbBusinessOrder> page = new Page<>();
        page.setCurrent(pageNum);
        page.setSize(pageSize);
        IPage<TbBusinessOrder> orderIPage = orderMapper.selectPage(page, Wrappers.lambdaQuery(TbBusinessOrder.class).like(TbBusinessOrder::getCustomerMobile, customerMobile));

        // 设置房源车位信息
        List<Long> houseIdList = orderIPage.getRecords().stream()
                .map(TbBusinessOrder::getHouseIds)
                .filter(Objects::nonNull)
                .map(houseIds -> Arrays.stream(houseIds.split(RsmsConstant.SPLIT_COMMA)).toList())
                .flatMap(Collection::stream)
                .map(Long::valueOf)
                .distinct()
                .toList();
        Map<Long, HouseInfoVo> houseInfoVoMap = dataServerClient.listHouseInfoByIds(houseIdList)
                .stream()
                .collect(Collectors.toMap(HouseInfoVo::getId, Function.identity(), (existing, next) -> existing));

        List<Long> parkingSpaceIdList = orderIPage.getRecords().stream()
                .map(TbBusinessOrder::getParkingIds)
                .filter(Objects::nonNull)
                .map(parkingSpaceIds -> Arrays.stream(parkingSpaceIds.split(RsmsConstant.SPLIT_COMMA)).toList())
                .flatMap(Collection::stream)
                .map(Long::valueOf)
                .distinct()
                .toList();
        Map<Long, ParkingSpaceResp> parkingSpaceRespMap = dataServerClient.getParkingSpaceByIdList(parkingSpaceIdList)
                .stream()
                .collect(Collectors.toMap(ParkingSpaceResp::getId, Function.identity(), (existing, next) -> existing));

        List<OrderResp> orderResps = orderIPage
                .getRecords()
                .stream()
                .map(order -> {
                    OrderResp orderResp = orderConvert.data2Resp(order);
                    if (!ObjectUtils.isEmpty(order.getHouseIds())) {
                        List<HouseInfoResp> houseInfoResps = Arrays.stream(order.getHouseIds().split(RsmsConstant.SPLIT_COMMA))
                                .map(Long::valueOf)
                                .map(houseId -> {
                                    if (!ObjectUtils.isEmpty(houseInfoVoMap.get(houseId))) {
                                        return houseConvert.vo2Resp(houseInfoVoMap.get(houseId));
                                    }
                                    return null;
                                })
                                .filter(Objects::nonNull)
                                .toList();
                        orderResp.setHouseInfo(houseInfoResps);
                        orderResp.setBusinessType(DropdownEnum.ORDER_BUSINESS_HOUSE.getDictKey());
                    } else if (!ObjectUtils.isEmpty(order.getParkingIds())) {
                        List<ParkingSpaceResp> parkingSpaceRespList = Arrays.stream(order.getParkingIds().split(RsmsConstant.SPLIT_COMMA))
                                .map(Long::valueOf)
                                .map(parkingSpaceId -> {
                                    if (!ObjectUtils.isEmpty(parkingSpaceRespMap.get(parkingSpaceId))) {
                                        return parkingSpaceRespMap.get(parkingSpaceId);
                                    }
                                    return null;
                                })
                                .filter(Objects::nonNull)
                                .toList();
                        orderResp.setParking(parkingSpaceRespList);
                        orderResp.setBusinessType(DropdownEnum.ORDER_BUSINESS_PARKING_SPACE.getDictKey());
                    } else if (!ObjectUtils.isEmpty(order.getNewHouseCommunityId())) {
                        NewHouseResp newHouseResp = dataServerClient.getNewHouseByIdList(List.of(order.getNewHouseCommunityId()))
                                .stream()
                                .collect(Collectors.toMap(NewHouseResp::getId, Function.identity(), (existing, next) -> existing))
                                .get(order.getNewHouseCommunityId());
                        NewHouseCommunityResp newHouseCommunityResp = new NewHouseCommunityResp();
                        newHouseCommunityResp.setNewHouseCommunityId(order.getNewHouseCommunityId());
                        newHouseCommunityResp.setBuilding(order.getBuilding());
                        newHouseCommunityResp.setUnit(order.getUnit());
                        newHouseCommunityResp.setHouseNumber(order.getHouseNumber());
                        if (!ObjectUtils.isEmpty(newHouseResp)) {
                            newHouseCommunityResp.setNewHouseCommunityName(newHouseResp.getCommunityName());
                        }
                        orderResp.setNewHouseCommunity(newHouseCommunityResp);
                        orderResp.setBusinessType(DropdownEnum.ORDER_BUSINESS_NEW_HOUSE_COMMUNITY.getDictKey());
                    }
                    return orderResp;
                })
                .toList();
        setSubOrderDetail(orderResps);
        Page<OrderResp> result = new Page<>(orderIPage.getCurrent(), orderIPage.getSize(), orderIPage.getTotal());
        result.setRecords(orderResps);
        return result;
    }

    /**
     * 填充子订单信息
     *
     * @param orderResps
     */
    private void setSubOrderDetail(List<OrderResp> orderResps) {
        if (ObjectUtils.isEmpty(orderResps)) {
            return;
        }
        List<Long> orderIdList = orderResps.stream()
                .map(OrderResp::getId)
                .toList();
        if (!ObjectUtils.isEmpty(orderIdList)) {
            Map<Long, List<OrderReceiveResp>> receiveMap = receiveMapper.selectList(Wrappers.lambdaQuery(TbBusinessSubOrderReceive.class)
                            .in(TbBusinessSubOrderReceive::getParentOrderId, orderIdList))
                    .stream()
                    .map(receiveConvert::data2Resp)
                    .collect(Collectors.groupingBy(OrderReceiveResp::getParentOrderId));
            Map<Long, List<OrderRefundResp>> refundMap = refundMapper.selectList(Wrappers.lambdaQuery(TbBusinessSubOrderRefund.class)
                            .in(TbBusinessSubOrderRefund::getParentOrderId, orderIdList))
                    .stream()
                    .map(refundConvert::data2Resp)
                    .collect(Collectors.groupingBy(OrderRefundResp::getParentOrderId));
            Map<Long, List<OrderCommissionResp>> commissionMap = commissionMapper.selectList(Wrappers.lambdaQuery(TbBusinessSubOrderCommission.class)
                            .in(TbBusinessSubOrderCommission::getParentOrderId, orderIdList))
                    .stream()
                    .map(commissionConvert::data2Resp)
                    .collect(Collectors.groupingBy(OrderCommissionResp::getParentOrderId));
            orderResps.forEach(item -> {
                if (!ObjectUtils.isEmpty(receiveMap.get(item.getId()))) {
                    item.setReceiveOrder(receiveMap.get(item.getId()));
                }
                if (!ObjectUtils.isEmpty(refundMap.get(item.getId()))) {
                    item.setRefundOrder(refundMap.get(item.getId()));
                }
                if (!ObjectUtils.isEmpty(commissionMap.get(item.getId()))) {
                    item.setCommissionOrder(commissionMap.get(item.getId()));
                }
            });
        }
    }


    /**
     * 创建订单
     *
     * @param orderAddParams
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TbBusinessOrder save(OrderAddParams orderAddParams) {
        TbBusinessOrder tbBusinessOrder = orderConvert.add2Data(orderAddParams);
        tbBusinessOrder.setId(snowFlake.nextId());
        tbBusinessOrder.setOrderCode(flowNumberUtil.getFlowNumber(BusinessPrefixEnum.ORDER_KEY_PREFIX.getCode(), 13));
        tbBusinessOrder.setOrderDate(LocalDateTime.now());
        tbBusinessOrder.setStatus(DropdownEnum.ORDER_STATUS_UNPAY.getDictKey());
        tbBusinessOrder.setCreateUser(AuthUtil.getLoginIdAsLong());
        orderMapper.insert(tbBusinessOrder);

        return addSubOrder(tbBusinessOrder.getId(),
                orderAddParams.getSubOrderReceiveAddParams(),
                orderAddParams.getSubOrderRefundAddParams(),
                orderAddParams.getSubOrderCommissionAddParams());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public TbBusinessOrder addSubOrder(Long orderId, List<SubOrderReceiveAddParams> subOrderReceiveAddParams,
                                       List<SubOrderRefundAddParams> subOrderRefundAddParams,
                                       List<SubOrderCommissionAddParams> subOrderCommissionAddParams) {
        BigDecimal amount = new BigDecimal("0.00");
        if (!ObjectUtils.isEmpty(subOrderReceiveAddParams)) {
            List<TbBusinessSubOrderReceive> subOrderReceives = subOrderReceiveAddParams
                    .stream()
                    .map(receiveConvert::add2Data)
                    .peek(item -> {
                        item.setId(snowFlake.nextId());
                        item.setParentOrderId(orderId);
                        item.setSubOrderCode(flowNumberUtil.getFlowNumber(BusinessPrefixEnum.SUB_ORDER_KEY_PREFIX.getCode(), 13));
                        item.setOrderStatus(DropdownEnum.ORDER_STATUS_UNPAY.getDictKey());
                        item.setCreateUser(AuthUtil.getLoginIdAsLong());
                    }).toList();
            receiveMapper.insert(subOrderReceives);
            amount = subOrderReceives.stream()
                    .map(TbBusinessSubOrderReceive::getPayAmount)
                    .reduce(amount, BigDecimal::add);
        }
        // 退款子订单创建
        if (!ObjectUtils.isEmpty(subOrderRefundAddParams)) {
            List<TbBusinessSubOrderRefund> orderRefunds = subOrderRefundAddParams.stream()
                    .map(refundConvert::add2Data)
                    .peek(item -> {
                        item.setId(snowFlake.nextId());
                        item.setParentOrderId(orderId);
                        item.setSubOrderCode(flowNumberUtil.getFlowNumber(BusinessPrefixEnum.SUB_ORDER_KEY_PREFIX.getCode(), 13));
                        item.setOrderStatus(DropdownEnum.ORDER_STATUS_UNREFUND.getDictKey());
                        item.setCreateUser(AuthUtil.getLoginIdAsLong());
                    }).toList();
            refundMapper.insert(orderRefunds);
            // 推送退款订单到收费系统
            launchRefund(orderRefunds.stream().map(TbBusinessSubOrderRefund::getId).toList());
        }

        if (!ObjectUtils.isEmpty(subOrderCommissionAddParams)) {
            List<TbBusinessSubOrderCommission> orderCommissions = subOrderCommissionAddParams
                    .stream()
                    .map(commissionConvert::add2Data)
                    .peek(item -> {
                        item.setId(snowFlake.nextId());
                        item.setParentOrderId(orderId);
                        item.setSubOrderCode(flowNumberUtil.getFlowNumber(BusinessPrefixEnum.SUB_ORDER_KEY_PREFIX.getCode(), 13));
                        item.setOrderStatus(DropdownEnum.ORDER_STATUS_UNCONVERT.getDictKey());
                        item.setCreateUser(AuthUtil.getLoginIdAsLong());
                    }).toList();
            commissionMapper.insert(orderCommissions);
        }
        orderMapper.update(Wrappers.lambdaUpdate(TbBusinessOrder.class)
                .eq(TbBusinessOrder::getId, orderId)
                .setSql(String.format("amount = amount + %s", amount)));
        return orderMapper.selectById(orderId);
    }

    /**
     * 大额收款，走转账支付
     * @param orderId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void bigAmountTransferPush(Long orderId) {
        TbBusinessOrder order = orderMapper.selectById(orderId);
        List<TbBusinessSubOrderReceive> subOrderReceiveList = receiveMapper.selectList(Wrappers.lambdaQuery(TbBusinessSubOrderReceive.class)
                .eq(TbBusinessSubOrderReceive::getParentOrderId, orderId));
        subOrderReceiveList = subOrderReceiveList.stream()
                .filter(subOrder -> {
                    return DropdownEnum.ORDER_STATUS_UNPAY.getDictKey().equals(subOrder.getOrderStatus());
                })
                .filter(subOrder -> {
                    // 大于 目标金额 走转账支付
                    return subOrder.getPayAmount().compareTo(minTransferAmount) > 0;
                }).peek(subOrder -> {
                    subOrder.setPayChannel(DropdownEnum.PAY_TYPE_BANKPAY.getDictKey());
                }).toList();
        if (ObjectUtils.isEmpty(subOrderReceiveList)) {
            log.info("大额转账订单推送....... 主订单ID：{} ，未查询到符合条件的大额子收款单， 推送逻辑处理完成", orderId);
            return;
        }
        receiveMapper.updateById(subOrderReceiveList);
        log.info("大额转账订单推送....... 主订单ID：{} ， 待推送的大额子收款单：{}", orderId, JacksonUtil.toJSON(subOrderReceiveList));
        subOrderReceiveList.forEach(subOrder ->{
            // 修改主订单、子订单、业务数据状态
            modifyPayOrderStatus(orderId, subOrder.getId(), order.getTransactionType(), subOrder.getPayChannel());
            // 推送订单到收费系统
            pushOrderToSF(order, subOrder, false, null);
        });
    }

    /**
     * 发起退款
     * @param refundOrderId 退款订单ID
     */
    private void launchRefund(List<Long> refundOrderId) {
        log.info("发起退款..... 退款子订单Id:{}", refundOrderId);
        if (ObjectUtils.isEmpty(refundOrderId)) {
            return;
        }
        List<TbBusinessSubOrderRefund> orderRefunds = refundMapper.selectBatchIds(refundOrderId);
        List<SFOrderRefundParam> sfOrderRefundParams = orderRefunds.stream()
                .peek(item -> {
                    // 退款订单状态改为退款中
                    refundMapper.update(Wrappers.lambdaUpdate(TbBusinessSubOrderRefund.class)
                            .eq(TbBusinessSubOrderRefund::getId, item.getId())
                            .set(TbBusinessSubOrderRefund::getOrderStatus, DropdownEnum.ORDER_STATUS_REFUNDING.getDictKey())
                    );
                    // 意向金 交易行订单状态改为 退款中
                    transactionInfoMapper.update(Wrappers.lambdaUpdate(TbBusinessTransactionInfo.class)
                            .eq(TbBusinessTransactionInfo::getId, item.getBusinessId())
                            .set(TbBusinessTransactionInfo::getOrderStatus, DropdownEnum.TRANSACTION_ORDER_STATUS_REFUNDING.getDictKey()));
                })
                .map(this::generateSFRefundParam)
                .toList();
        log.info("发起退款参数生成完成：{}", JacksonUtil.toJSON(sfOrderRefundParams));
        for (SFOrderRefundParam orderRefundParam : sfOrderRefundParams) {
            log.info("调用 third 发起退款：参数：{}", JacksonUtil.toJSON(orderRefundParam));
            SfOrderRefundResp refundResp = thirdServerClient.refund(orderRefundParam);
            log.info("调用 third 发起退款：响应：{}", JacksonUtil.toJSON(refundResp));
            if (ObjectUtils.isEmpty(refundResp)) {
                throw new BusinessException(CommonEnum.SYSTEM_ERROR);
            }
            if (!"0".equals(refundResp.getCode())) {
                throw new BusinessException(refundResp.getMessage(), CommonEnum.OTHER_ERROR.getCode());
            }
            // 记录收费系统退款单号
            refundMapper.update(Wrappers.lambdaUpdate(TbBusinessSubOrderRefund.class)
                    .eq(TbBusinessSubOrderRefund::getSubOrderCode, orderRefundParam.getRefundNo())
                    .set(TbBusinessSubOrderRefund::getSfRefundOrderNum, refundResp.getContent()));
        }
    }

    /**
     * 生成 收费退款接口参数
     * @param orderRefund
     * @return
     */
    private SFOrderRefundParam generateSFRefundParam(TbBusinessSubOrderRefund orderRefund) {
        TbBusinessOrder order = orderMapper.selectById(orderRefund.getParentOrderId());
        SFOrderRefundParam refundParam = new SFOrderRefundParam();
        refundParam.setOrderNo(order.getOrderCode());
        refundParam.setSource(sfSource);
        refundParam.setRefundNo(orderRefund.getSubOrderCode());
        refundParam.setRefundAmount(orderRefund.getAmount());
        refundParam.setPayStatus(RsmsConstant.FLAG_NO);
        refundParam.setRefundTime(LocalDateTime.now());
        refundParam.setPayType(orderRefund.getRefundChannel());
        refundParam.setAcceptAccountName(orderRefund.getAccountName());
        refundParam.setAcceptAccountCity(orderRefund.getCity());
        refundParam.setAcceptBankAccountNo(orderRefund.getBankNum());
        refundParam.setAcceptAccountOpeningBank(orderRefund.getBankName());
        refundParam.setMemo(orderRefund.getRemark());
        TbBusinessSubOrderReceive orderReceive = receiveMapper.selectById(orderRefund.getReceiveSubOrderId());
        SfSubOrderRefundParam sfSubOrderRefundParam = new SfSubOrderRefundParam();
        sfSubOrderRefundParam.setAmount(orderRefund.getAmount());
        sfSubOrderRefundParam.setSubOrderNo(orderReceive.getSubOrderCode());
        refundParam.setSubOrders(List.of(
                sfSubOrderRefundParam
        ));
        depositMapper.selectList(Wrappers.lambdaQuery(TbBusinessDeposit.class)
                        .eq(TbBusinessDeposit::getOrderId, orderRefund.getParentOrderId()))
                .stream()
                .findAny()
                .ifPresent(deposit -> {
                    // 创建人即为操作人
                    String operatorName = authorServerClient.getUserDetailsMicro(List.of(deposit.getCreateUser()))
                            .stream()
                            .collect(Collectors.toMap(UserDetailsMicroResp::getId, UserDetailsMicroResp::getEmpName))
                            .get(deposit.getCreateUser());
                    refundParam.setOperatorName(operatorName);

                    // 设置项目code
                    if (!ObjectUtils.isEmpty(deposit.getHouseDesc())) {
                        List<Long> communityIdList = dataServerClient.listHouseInfoByIds(Arrays.stream(deposit.getHouseDesc().split(RsmsConstant.SPLIT_COMMA))
                                        .map(Long::valueOf)
                                        .toList())
                                .stream()
                                .map(HouseInfoVo::getCommunityId)
                                .distinct()
                                .toList();
                        dataServerClient.getCommunityListByIds(communityIdList)
                                .stream()
                                .findAny()
                                .ifPresent(community -> {
                                    refundParam.setCommunityMdmCode(community.getCommunityCode());
                                });
                    } else if (!ObjectUtils.isEmpty(deposit.getParkingSpaceDesc())) {
                        // 车位查询对应项目code
                        List<Long> communityIdList = dataServerClient.getParkingSpaceByIdList(Arrays.stream(deposit.getParkingSpaceDesc().split(RsmsConstant.SPLIT_COMMA))
                                        .map(Long::valueOf)
                                        .toList())
                                .stream()
                                .map(ParkingSpaceResp::getCommunityId)
                                .distinct()
                                .toList();
                        dataServerClient.getCommunityListByIds(communityIdList)
                                .stream()
                                .findAny()
                                .ifPresent(community -> {
                                    refundParam.setCommunityMdmCode(community.getCommunityCode());
                                });
                    }
                });
        return refundParam;
    }

    /**
     * POS 支付订单
     * 1. 修改 主订单状态、子订单状态、业务数据表对应数据的 状态
     * 2. 调用收费系统接口
     *
     * @param params
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void posPay(PayOrderReq params) {
        if (ObjectUtils.isEmpty(params.getOrderId()) || ObjectUtils.isEmpty(params.getSubOrderId())) {
            throw new BusinessException(BusinessEnum.PARAMETER_EXCEPTION);
        }
        RLock lock = redissonClient.getLock(String.format(RedisConstant.TRADE_ORDER_SERIAL_NUMBER_PREFIX, params.getOrderId()));
        try {
            lock.lock();
            TbBusinessOrder order = orderMapper.selectById(params.getOrderId());
            TbBusinessSubOrderReceive orderReceives = receiveMapper.selectById(params.getSubOrderId());
            // 检查主订单、子订单状态
            checkOrderStatus(order, orderReceives);
            // POS 支付方式
            String payChannel = DropdownEnum.PAY_TYPE_POSPAY.getDictKey();
            // 1. 先改变订单状态、业务数据状态
            // 主、子订单状态改变
            modifyPayOrderStatus(order.getId(), orderReceives.getId(), order.getTransactionType(), payChannel);
            // 2. 查询 对应订单，调用 收费系统 接口
            // pos 支付时，推送收费系统的订单均为 未支付订单
            pushOrderToSF(order, orderReceives, false, null);
        } catch (Exception e) {
            log.error("POS 支付异常.....", e);
            throw e;
        }finally {
            lock.unlock();
        }
    }

    /**
     * TODO 微信支付、支付宝支付发起
     * @param params
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ThirdPayResultResp thirdPay(PayOrderReq params) {
        // 支付参数检查
        checkThirdPayParam(params);
        // 生成下单流水号对象
        String serialNumberOrder = generateSerialNumber(String.valueOf(params.getOrderId()), 3);
        String serialNumberSubOrder = generateSerialNumber(String.valueOf(params.getSubOrderId()), 3);
        TbBusinessTradeOrderLog tradeOrderLog = generateTradeOrderLog(params.getOrderId(), params.getSubOrderId(), serialNumberOrder, serialNumberSubOrder);
        // 申请锁
        RLock lock = redissonClient.getLock(String.format(RedisConstant.TRADE_ORDER_SERIAL_NUMBER_PREFIX, params.getOrderId()));
        try {
            // 加锁
            lock.lock();
            log.info("获取到锁，开始聚合支付逻辑........");
            TbBusinessOrder order = orderMapper.selectById(params.getOrderId());
            TbBusinessSubOrderReceive orderReceives = receiveMapper.selectById(params.getSubOrderId());
            // 检查主订单、子订单状态
            checkOrderStatus(order, orderReceives);

            // 1. 先改变订单状态、业务数据状态
            // 主、子订单状态改变
            modifyPayOrderStatus(order.getId(), orderReceives.getId(), order.getTransactionType(), params.getPayMode());
            existTradeOrderSerial(order.getId(), orderReceives.getId());

            // 微信支付
            String paymentMethod = null;
            if (DropdownEnum.PAY_TYPE_WXPAY.getDictKey().equals(params.getPayMode())) {
                paymentMethod = "3";
            } else if (DropdownEnum.PAY_TYPE_ALIPAY.getDictKey().equals(params.getPayMode())) {
                // 支付宝支付
                paymentMethod = "2";
            }
            // 更新下单流水号对象
            tradeOrderLog.setOrderCode(order.getOrderCode());
            tradeOrderLog.setSubOrderCode(orderReceives.getSubOrderCode());
            String communityCode = getOrderCommunityCode(order);
            int type = 4;   // 默认查询意向金下的 收费项
            if (DropdownEnum.ORDER_SOURCE_TYPE_YJ.getDictKey().equals(order.getTransactionType())) {
                type = 5;   // 佣金为 5
            }
            checkSubItem(communityCode, orderReceives.getReceiveCommunityId(), type);
            // 生成下单参数
            SFOrderTradeCreateParam param = generateTradeCreateParam(params,
                    communityCode,
                    paymentMethod,
                    order,
                    orderReceives,
                    serialNumberOrder,
                    serialNumberSubOrder);
            SFOrderTradeCreateResp tradedOrder = tradeOrder(tradeOrderLog,
                    param,
                    params.getOrderId(),
                    params.getSubOrderId());
            ThirdPayResultResp resultResp = generatePayResult(params.getTradeScene(), tradedOrder.getContent());
            log.info("聚合支付返回：{}", resultResp);
            return resultResp;
        } catch (Exception e) {
            log.error("聚合支付异常。。。。", e);
            tradeOrderLog.setStatus(DropdownEnum.TRADE_ORDER_LOG_STATUS_FAIL.getDictKey());
            throw e;
        }finally {
            log.info("新增订单流水:{}", tradeOrderLog);
            try {
                tradeOrderLogService.insertTradeOrderLog(tradeOrderLog);
            } catch (Exception e) {
                log.info("新增订单流水异常", e);
            }
            lock.unlock();
        }
    }

    /**
     * 检查项目下是否存在子收费项，
     * 由于调用收费 在线支付下单 接口时，收费不关心子收费项，而支付成功后同步订单时，又需要上送子收费项，故需要租售在下单时即校验子收费项
     *
     * @param communityCode
     * @param subItemId
     * @param type 4-意向金 5-佣金
     */
    public void checkSubItem(String communityCode, String subItemId, Integer type) {
        log.info("下单前检查收费项是否存在：项目 code ：{} 二级收费项ID :{}", communityCode, subItemId);
        SFOrderItemParam orderItemParam = new SFOrderItemParam();
        orderItemParam.setCommunityMdmCode(communityCode);
        orderItemParam.setType(type);
        log.info("调用收费系统 查询收费项接口：参数：{}", JacksonUtil.toJSON(orderItemParam));
        SFOrderItemResp sfOrderItemResp = thirdServerClient.getItems(orderItemParam);
        log.info("调用收费系统 查询收费项接口：响应：{}", JacksonUtil.toJSON(sfOrderItemResp));
        if (ObjectUtils.isEmpty(sfOrderItemResp)) {
            throw new BusinessException(CommonEnum.SYSTEM_ERROR);
        }
        if (!"0".equals(sfOrderItemResp.getCode())) {
            throw new BusinessException(sfOrderItemResp.getMessage(), CommonEnum.OTHER_ERROR.getCode());
        }
        if (ObjectUtils.isEmpty(sfOrderItemResp.getContent())) {
            throw new BusinessException(CommonEnum.SYSTEM_ERROR);
        }
        Optional<Long> subItemIdExist = sfOrderItemResp.getContent()
                .stream()
                .flatMap(sfOrderItemContentResp -> sfOrderItemContentResp.getItems().stream())
                .map(SFItemResp::getItemId)
                .filter(id -> subItemId.equals(String.valueOf(id)))
                .findAny();
        if (!subItemIdExist.isPresent()) {
            throw new BusinessException(BusinessEnum.RECEIVECOMMUNITY_NOT_EXIST_ERROR);
        }
    }

    /**
     * 生成下单返回结果
     * @param tradeScene
     * @param content
     * @return
     */
    private ThirdPayResultResp generatePayResult(String tradeScene, SFOrderCreateContentResp content) {
        ThirdPayResultResp thirdPayResultResp = new ThirdPayResultResp();
        thirdPayResultResp.setTradeScene(tradeScene);
        // 微信 小程序、公众号支付时，返回类型处理
        if (DropdownEnum.PAYMENT_ORDER_TYPE_WXSMALLPROGRAM.getDictKey().equals(tradeScene)) {
            BeanUtils.copyProperties(content, thirdPayResultResp, "tradeScene");
        } else if (DropdownEnum.PAYMENT_ORDER_TYPE_WXH5.getDictKey().equals(tradeScene)) {
            // 微信 H5 返回类型处理
            BeanUtils.copyProperties(ThirdPayUtil.getCmbPayInfo(content, active), thirdPayResultResp, "tradeScene");
        } else if (DropdownEnum.PAYMENT_ORDER_TYPE_ALIH5.getDictKey().equals(tradeScene)
                || DropdownEnum.PAYMENT_ORDER_TYPE_ALISMALLPROGRAM.getDictKey().equals(tradeScene)) {
            // 支付宝 H5 返回类型处理
            thirdPayResultResp.setAliQrCode(content.getPayInfo().get("qrCode").asText());
            thirdPayResultResp.setOutTradeNo(content.getOutTradeNo());
        }
        return thirdPayResultResp;
    }

    /**
     * 下单
     * @param tradeOrderLog
     * @param param
     * @param orderId
     * @param subOrderId
     * @return
     */
    private SFOrderTradeCreateResp tradeOrder(TbBusinessTradeOrderLog tradeOrderLog,
                                              SFOrderTradeCreateParam param,
                                              Long orderId,
                                              Long subOrderId
    ) {
        tradeOrderLog.setReqBody(JacksonUtil.toJSON(param));
        // 调用收费接口，下单
        log.info("主订单：{} 子订单：{} 下单参数：{} ", orderId, subOrderId, param);
        SFOrderTradeCreateResp trade = thirdServerClient.createTrade(param);
        log.info("主订单：{} 子订单：{} 响应：{}", orderId, subOrderId, trade);

        tradeOrderLog.setRespBody(JacksonUtil.toJSON(trade));

        // 检查下单返回结果
        if (ObjectUtils.isEmpty(trade)) {
            throw new BusinessException(CommonEnum.SYSTEM_ERROR);
        }
        if (!"0".equals(trade.getCode())) {
            throw new BusinessException(trade.getMessage(), CommonEnum.OTHER_ERROR.getCode());
        }
        if (ObjectUtils.isEmpty(trade.getContent())) {
            throw new BusinessException(CommonEnum.SYSTEM_ERROR);
        }

        tradeOrderLog.setStatus(DropdownEnum.TRADE_ORDER_LOG_STATUS_SUCCESS.getDictKey());
        return trade;
    }

    private void checkThirdPayParam(PayOrderReq params) {
        if (ObjectUtils.isEmpty(params)
                || (!DropdownEnum.PAY_TYPE_WXPAY.getDictKey().equals(params.getPayMode())
                && !DropdownEnum.PAY_TYPE_ALIPAY.getDictKey().equals(params.getPayMode()))
        ) {
            throw new BusinessException(BusinessEnum.PARAMETER_EXCEPTION);
        }

        if (ObjectUtils.isEmpty(params.getOrderId()) || ObjectUtils.isEmpty(params.getSubOrderId())) {
            throw new BusinessException(BusinessEnum.PARAMETER_EXCEPTION);
        }
    }

    private TbBusinessTradeOrderLog generateTradeOrderLog(Long orderId, Long subOrderId, String serialNumberOrder, String serialNumberSubOrder) {
        TbBusinessTradeOrderLog tradeOrderLog = new TbBusinessTradeOrderLog();
        tradeOrderLog.setId(snowFlake.nextId());
        tradeOrderLog.setOrderId(orderId);
        tradeOrderLog.setSubOrderId(subOrderId);
        tradeOrderLog.setOrderSerialNumber(serialNumberOrder);
        tradeOrderLog.setSubOrderSerialNumber(serialNumberSubOrder);

        return tradeOrderLog;
    }

    /**
     * 生成流水号
     * @param prefix
     * @param suffixSize
     * @return
     */
    private String generateSerialNumber(String prefix, Integer suffixSize) {
        return flowNumberUtil.getFlowNumber(String.format("%s_", prefix), suffixSize);
    }
    /**
     * 检查订单状态
     * @param order
     * @param subOrderReceive
     */
    private void checkOrderStatus(TbBusinessOrder order, TbBusinessSubOrderReceive subOrderReceive) {
        if (ObjectUtils.isEmpty(order)) {
            throw new BusinessException(BusinessEnum.ORDER_NOT_EXIST_ERROR);
        }
        if (ObjectUtils.isEmpty(subOrderReceive)) {
            throw new BusinessException(BusinessEnum.SUB_ORDER_NOT_EXIST_ERROR);
        }
        if (!DropdownEnum.ORDER_STATUS_UNPAY.getDictKey().equals(subOrderReceive.getOrderStatus())) {
            throw new BusinessException(BusinessEnum.ORDRE_STATUS_ERROR);
        }
    }

    /**
     * 检查当前订单是否已下单成功
     * @param orderId
     * @param subOrderId
     */
    private void existTradeOrderSerial(Long orderId, Long subOrderId) {
        TbBusinessSubOrderReceive orderReceive = receiveMapper.selectOne(Wrappers.lambdaQuery(TbBusinessSubOrderReceive.class)
                .eq(TbBusinessSubOrderReceive::getParentOrderId, orderId)
                .eq(TbBusinessSubOrderReceive::getId, subOrderId));
        List<TbBusinessTradeOrderLog> tradeOrderLogs = tradeOrderLogService.list(Wrappers.lambdaQuery(TbBusinessTradeOrderLog.class)
                .eq(TbBusinessTradeOrderLog::getOrderId, orderId)
                .eq(TbBusinessTradeOrderLog::getSubOrderId, subOrderId)
                .eq(TbBusinessTradeOrderLog::getStatus, DropdownEnum.TRADE_ORDER_LOG_STATUS_SUCCESS.getDictKey()));
        if (!ObjectUtils.isEmpty(tradeOrderLogs) && DropdownEnum.ORDER_STATUS_PAYING.getDictKey().equals(orderReceive.getOrderStatus())) {
            throw new BusinessException(BusinessEnum.CURRENT_ORDER_PAYING);
        }
    }

    private String getOrderCommunityCode(TbBusinessOrder order) {
        String communityCode = null;
        List<Long> communityIdList = null;
        if (!ObjectUtils.isEmpty(order.getHouseIds())) {
            communityIdList = dataServerClient.listHouseInfoByIds(Arrays.stream(order.getHouseIds().split(RsmsConstant.SPLIT_COMMA)).map(Long::valueOf).toList()).stream()
                    .map(HouseInfoVo::getCommunityId)
                    .toList();
        } else if (!ObjectUtils.isEmpty(order.getParkingIds())) {
            communityIdList = dataServerClient.getParkingSpaceByIdList(Arrays.stream(order.getParkingIds().split(RsmsConstant.SPLIT_COMMA)).map(Long::valueOf).toList()).stream()
                    .map(ParkingSpaceResp::getCommunityId)
                    .toList();
        }
        if (!ObjectUtils.isEmpty(communityIdList)) {
            Optional<CommunityResp> any = dataServerClient.getCommunityListByIds(communityIdList)
                    .stream()
                    .findAny();
            if (any.isPresent()) {
                communityCode = any.get().getCommunityCode();
            }
        } else if (!ObjectUtils.isEmpty(order.getNewHouseCommunityId())){    // 新房项目获取 项目Code
            Optional<NewHouseResp> any = dataServerClient.getNewHouseByIdList(List.of(order.getNewHouseCommunityId()))
                    .stream()
                    .findAny();
            if (any.isPresent()) {
                communityCode = any.get().getCommunityCode();
            }
        }
        return communityCode;
    }

    private SFOrderTradeCreateParam generateTradeCreateParam(PayOrderReq payOrderParams,
                                                             String communityMdmCode,
                                                             String paymentMethod,
                                                             TbBusinessOrder order,
                                                             TbBusinessSubOrderReceive subOrder,
                                                             String serialNumberOrder,
                                                             String serialNumberSubOrder
    ) {
        SFOrderTradeCreateParam param = new SFOrderTradeCreateParam();
        param.setCommunityMdmCode(communityMdmCode);
        param.setOutTradeNo(serialNumberOrder);
        param.setTotalFee(String.valueOf(subOrder.getPayAmount().multiply(new BigDecimal(100)).intValue()));
        Arrays.stream(DropdownEnum.values())
                .filter(value -> value.getDictKey().equals(order.getTransactionType()))
                .findAny()
                .ifPresent(consumer -> {
                    param.setBody(consumer.getDictValue());
                });
        LocalDateTime localDateTime = LocalDateTime.now().plusMinutes(orderTimeOut);
        DateTimeFormatter sdf = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        param.setTimeExpire(localDateTime.format(sdf));

        param.setMchCreateIp(publicIp);
        // 支付终端 电商：MALL 租售：LEASE  公区：PUBLIC_AREA  朝昔：YUEHOME_PAY
        param.setPaymentTerminal(sfSource);

        param.setTradeScene(tradeSceneMap.get(payOrderParams.getTradeScene()));
        param.setPaymentMethod(paymentMethod);

//        if (DropdownEnum.TRADE_SCENE_ALI_JSAPI.getDictKey().equals(payOrderParams.getTradeScene())) {
//            param.setBuyerLogonId(payOrderParams.getMobile());
//        }
        /**
         * 商户微信公众号appid，app支付时,为在微信开放平台上申请的APPID
         * 小程序支付必填：当前调起支付的小程序APPID
         * APP支付必填：子商户在微信开放平台上申请的APPID
         * 公众号支付：公众号APPID
         * 其他支付模式可不填
         */
        if (!ObjectUtils.isEmpty(payOrderParams.getTradeScene())) {
            if (DropdownEnum.PAYMENT_ORDER_TYPE_WXSMALLPROGRAM.getDictKey().equals(payOrderParams.getTradeScene())) {
                param.setSubAppId(sfWxSubAppId);
                String openIdByMobile = mobileServerClient.getOpenIdByMobile(payOrderParams.getMobile());
                param.setSubOpenId(openIdByMobile);
            }
        }
        param.setPayMember(order.getCustomerName());
        CustomerSourceResp customerSourceResp = dataServerClient.innerQueryCustomerByMobile(order.getCustomerMobile());
        if (!ObjectUtils.isEmpty(customerSourceResp)) {
            param.setPayMemberId(String.valueOf(customerSourceResp.getId()));
        }

        SFOrderTradePayItems payItems = new SFOrderTradePayItems();
        payItems.setCommunityMdmCode(communityMdmCode);
        payItems.setItemId(Integer.valueOf(subOrder.getReceiveCommunityId()));
        payItems.setBody(subOrder.getReceiveCommunityName());
        payItems.setMoney(subOrder.getPayAmount().multiply(new BigDecimal("100")).intValue());
        payItems.setSubOutTradeNo(serialNumberSubOrder);
        param.setPayItems(List.of(payItems));
        return param;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelPay(CancelPayReq cancelPayReq) {
        RLock lock = redissonClient.getLock(String.format(RedisConstant.TRADE_ORDER_SERIAL_NUMBER_PREFIX, cancelPayReq.getOrderId()));
        try {
            lock.lock();
            TbBusinessOrder order = orderMapper.selectById(cancelPayReq.getOrderId());
            TbBusinessSubOrderReceive orderReceive = receiveMapper.selectOne(Wrappers.lambdaQuery(TbBusinessSubOrderReceive.class)
                    .eq(TbBusinessSubOrderReceive::getId, cancelPayReq.getSubOrderId())
                    .eq(TbBusinessSubOrderReceive::getParentOrderId, cancelPayReq.getOrderId()));
            if (ObjectUtils.isEmpty(orderReceive)) {
                throw new BusinessException(BusinessEnum.SUB_ORDER_NOT_EXIST_ERROR);
            }
            if (!DropdownEnum.ORDER_STATUS_PAYING.getDictKey().equals(orderReceive.getOrderStatus())) {
                throw new BusinessException(BusinessEnum.ORDRE_STATUS_ERROR);
            }
            if (orderReceive.getPayAmount().compareTo(minTransferAmount) > 0) {
                throw new BusinessException(BusinessEnum.CANCEL_TRANSFER_ORDER_ERROR);
            }
            // POS 支付订单无法手动取消
            if (DropdownEnum.PAY_TYPE_POSPAY.getDictKey().equals(orderReceive.getPayChannel())) {
                throw new BusinessException(BusinessEnum.CANCEL_POS_PAYMENT_ERROR);
            }

            // 这里关闭订单后，订单的状态回退逻辑与 处理订单回调时 失败状态下的逻辑相同
            payCallBackOnOrder(order.getOrderCode(), orderReceive.getSubOrderCode(), DropdownEnum.SF_ROLLBACK_STATUS_FAIL.getDictKey(), orderReceive.getPayChannel(), orderReceive.getPayAmount());

            // 业务数据表 订单状态数据改变
            if (DropdownEnum.ORDER_SOURCE_TYPE_YJ.getDictKey().equals(order.getTransactionType())) {
                // 成交报告-收佣计划行 存在主、子订单ID，故根据 主、子订单ID 更新状态
                receiveCommissionPlanMapper.update(Wrappers.lambdaUpdate(TbBusinessReceiveCommissionPlan.class)
                        .eq(TbBusinessReceiveCommissionPlan::getOrderId, order.getId())
                        .eq(TbBusinessReceiveCommissionPlan::getSubOrderId, orderReceive.getId())
                        .set(TbBusinessReceiveCommissionPlan::getOrderStatus, DropdownEnum.TRANSFER_RECEIVE_UNPAY.getDictKey()));
            }else {
                // 意向金-交易明细行 不存在主订单ID,故根据 子订单ID 更新状态即可
                transactionInfoMapper.update(Wrappers.lambdaUpdate(TbBusinessTransactionInfo.class)
                        .eq(TbBusinessTransactionInfo::getSubOrderId, orderReceive.getId())
                        .set(TbBusinessTransactionInfo::getOrderStatus, DropdownEnum.TRANSACTION_ORDER_STATUS_UNPAY.getDictKey()));
            }

            // 下单成功状态 的流水号 改为取消状态
            tradeOrderLogService.update(Wrappers.lambdaUpdate(TbBusinessTradeOrderLog.class)
                    .eq(TbBusinessTradeOrderLog::getOrderId, cancelPayReq.getOrderId())
                    .eq(TbBusinessTradeOrderLog::getSubOrderId, cancelPayReq.getSubOrderId())
                    .eq(TbBusinessTradeOrderLog::getStatus, DropdownEnum.TRADE_ORDER_LOG_STATUS_SUCCESS.getDictKey())
                    .set(TbBusinessTradeOrderLog::getStatus, DropdownEnum.TRADE_ORDER_LOG_STATUS_MANUALLY_CANCEL.getDictKey()));
            log.info("取消支付订单：主订单ID：{} 子订单ID/号：{}/{} ......", orderReceive.getParentOrderId(), orderReceive.getId(), orderReceive.getSubOrderCode());
        } finally {
            lock.unlock();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrderResp queryTradeOrderInfo(Long orderId, Long subOrderId) {
        OrderResp orderResp = null;
        RLock lock = redissonClient.getLock(String.format(RedisConstant.TRADE_ORDER_SERIAL_NUMBER_PREFIX, orderId));
        try {
            lock.lock();
            TbBusinessSubOrderReceive orderReceive = receiveMapper.selectOne(Wrappers.lambdaQuery(TbBusinessSubOrderReceive.class)
                    .eq(TbBusinessSubOrderReceive::getId, subOrderId)
                    .eq(TbBusinessSubOrderReceive::getParentOrderId, orderId));
            if (ObjectUtils.isEmpty(orderReceive)) {
                throw new BusinessException(BusinessEnum.SUB_ORDER_NOT_EXIST_ERROR);
            }
            // 仅支付中的订单可以发起查询，避免查询时，如果订单为非支付中状态，则标识已经收到支付回调
            if (DropdownEnum.ORDER_STATUS_PAYING.getDictKey().equals(orderReceive.getOrderStatus())) {
                List<TbBusinessTradeOrderLog> orderLogs = tradeOrderLogService.list(Wrappers.lambdaQuery(TbBusinessTradeOrderLog.class)
                        .eq(TbBusinessTradeOrderLog::getOrderId, orderId)
                        .eq(TbBusinessTradeOrderLog::getSubOrderId, subOrderId)
                        .eq(TbBusinessTradeOrderLog::getStatus, DropdownEnum.TRADE_ORDER_LOG_STATUS_SUCCESS.getDictKey()));
                if (ObjectUtils.isEmpty(orderLogs) || orderLogs.size() > 1) {
                    throw new BusinessException(BusinessEnum.ORDER_SERIAL_ERROR);
                }
                Optional<TbBusinessTradeOrderLog> first = orderLogs.stream()
                        .findFirst();
                if (first.isPresent()) {
                    TbBusinessTradeOrderLog consumer = first.get();
                    SFOrderPayQueryResp sfOrderPayQueryResp = queryPayResult(consumer);
                    SFOrderPayQueryBusinessResp content = sfOrderPayQueryResp.getContent();

                    log.info("查询下单支付结果结束，开始同步更新订单业务数据状态......");
                    log.info("调用收费回调处理逻辑，进行订单业务数据状态同步，主流水号：{} 收费返回状态：{} .....",
                            content.getOutTradeNo(), content.getTradeState());

                    handleThirdPayCallBack(true,
                            content.getTransactionId(),
                            content.getOutTradeNo(),
                            null,
                            content.getTradeState(),
                            content.getPayChannel(),
                            content.getPayFinishTime(),
                            new BigDecimal(content.getTotalFee()).divide(new BigDecimal("100"))
                    );
                }
            }

            // 生成返回信息
            TbBusinessOrder order = orderMapper.selectById(orderId);

            // 设置房源车位信息
            Map<Long, HouseInfoVo> houseInfoVoMap = new LinkedHashMap<>();
            if (!ObjectUtils.isEmpty(order.getHouseIds())) {
                List<Long> houseIdList = Arrays.stream(order.getHouseIds().split(RsmsConstant.SPLIT_COMMA)).map(Long::valueOf)
                        .distinct().toList();
                houseInfoVoMap.putAll(queryHouseInfoMap(houseIdList));
            }
            Map<Long, ParkingSpaceResp> parkingSpaceRespMap = new LinkedHashMap<>();
            if (!ObjectUtils.isEmpty(order.getParkingIds())) {
                List<Long> parkingSpaceIdList = Arrays.stream(order.getParkingIds().split(RsmsConstant.SPLIT_COMMA)).map(Long::valueOf)
                        .distinct().toList();
                parkingSpaceRespMap.putAll(queryParkingSpaceInfoMap(parkingSpaceIdList));
            }
            orderResp = generateOrderResp(order, houseInfoVoMap, parkingSpaceRespMap);
            setSubOrderDetail(List.of(orderResp));
        } catch (Exception e) {
            log.error("查询下单支付结果异常....", e);
        }finally {
            lock.unlock();
        }
        return orderResp;
    }

    /**
     * 调用收费系统接口，查询下单订单的支付结果
     * @param orderLogs
     * @return
     */
    private SFOrderPayQueryResp queryPayResult(TbBusinessTradeOrderLog orderLogs) {
        SFOrderTradeCreateParam param = JacksonUtil.fromJSON(orderLogs.getReqBody(), new TypeReference<SFOrderTradeCreateParam>() {
        });
        if (ObjectUtils.isEmpty(param)) {
            throw new BusinessException(BusinessEnum.ORDER_SERIAL_ERROR);
        }
        SFOrderPayQueryParam payQueryParam = generatePayQuery(param.getCommunityMdmCode(), orderLogs.getOrderSerialNumber());
        log.info("查询订单支付参数：{}", JacksonUtil.toJSON(payQueryParam));
        SFOrderPayQueryResp sfOrderPayQueryResp = thirdServerClient.payQuery(payQueryParam);
        log.info("查询订单支付返回：{}", JacksonUtil.toJSON(sfOrderPayQueryResp));
        if (ObjectUtils.isEmpty(sfOrderPayQueryResp)) {
            throw new BusinessException(CommonEnum.SYSTEM_ERROR);
        }
        if (!"0".equals(sfOrderPayQueryResp.getCode())) {
            throw new BusinessException(sfOrderPayQueryResp.getMessage(), CommonEnum.OTHER_ERROR.getCode());
        }
        if (ObjectUtils.isEmpty(sfOrderPayQueryResp.getContent())) {
            throw new BusinessException(CommonEnum.SYSTEM_ERROR);
        }
        return sfOrderPayQueryResp;
    }

    private SFOrderPayQueryParam generatePayQuery(String communityMdmCode, String outTradeNo) {
        SFOrderPayQueryParam payQueryParam = new SFOrderPayQueryParam();
        payQueryParam.setCommunityMdmCode(communityMdmCode);
        payQueryParam.setPaymentTerminal(sfSource);
        payQueryParam.setOutTradeNo(outTradeNo);
        payQueryParam.setSyncQuery(true);
        return payQueryParam;
    }

    /**
     *
     * @param orderId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void prepareConvertCommission(Long orderId) {
        // 检查是否存在转佣订单，存在准备数据，调用收费系统转佣接口
        List<TbBusinessSubOrderCommission> orderCommissions = commissionMapper.selectList(Wrappers.lambdaQuery(TbBusinessSubOrderCommission.class)
                .eq(TbBusinessSubOrderCommission::getParentOrderId, orderId));
        // 本地库都没有转佣数据
        if (ObjectUtils.isEmpty(orderCommissions)) {
            return;
        }
        TbBusinessOrder order = orderMapper.selectById(orderId);

        List<Long> reportIdList = receiveCommissionPlanMapper.selectBatchIds(orderCommissions.stream().map(TbBusinessSubOrderCommission::getBusinessId).distinct().toList())
                .stream().map(TbBusinessReceiveCommissionPlan::getReportId)
                .distinct()
                .toList();
        List<TbBusinessTransactionReport> reports = reportMapper.selectBatchIds(reportIdList);
        List<Long> houseIdList = reports
                .stream()
                .map(TbBusinessTransactionReport::getHouseId)
                .filter(Objects::nonNull)
                .toList();
        List<Long> parkingSpaceId = reports
                .stream()
                .map(TbBusinessTransactionReport::getParkingSpaceId)
                .filter(Objects::nonNull)
                .toList();
        List<Long> newHouseCommunityId = reports.stream()
                .map(TbBusinessTransactionReport::getNewHouseCommunityId)
                .filter(Objects::nonNull)
                .toList();
        List<Long> communityIdList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(houseIdList)) {    // 二手房
            communityIdList = dataServerClient.listHouseInfoByIds(houseIdList)
                    .stream()
                    .map(HouseInfoVo::getCommunityId)
                    .toList();
        } else if (!ObjectUtils.isEmpty(parkingSpaceId)) {  // 车位
            communityIdList = dataServerClient.getParkingSpaceByIdList(parkingSpaceId)
                    .stream()
                    .map(ParkingSpaceResp::getCommunityId)
                    .toList();
        }
        String communityCode = null;
        if (!ObjectUtils.isEmpty(communityIdList)) {
            Optional<CommunityResp> any = dataServerClient.getCommunityListByIds(communityIdList)
                    .stream().findAny();
            if (any.isPresent()) {
                communityCode = any.get().getCommunityCode();
            }
        } else if (!ObjectUtils.isEmpty(newHouseCommunityId)) { // 新房项目获取 code
            Optional<NewHouseResp> any = dataServerClient.getNewHouseByIdList(newHouseCommunityId)
                    .stream()
                    .findAny();
            if (any.isPresent()) {
                communityCode = any.get().getCommunityCode();
            }
        }
        String subOrderCode = null;
        Optional<String> anyCommissionCode = orderCommissions.stream()
                .map(TbBusinessSubOrderCommission::getSubOrderCode)
                .findAny();
        if (anyCommissionCode.isPresent()) {
            subOrderCode = anyCommissionCode.get();
        }
        // 查询收费系统子订单接口，是否存在转佣的子订单号，存在则不进行转佣订单的推送，不存在则推送转佣子订单
        SFOrderDetailSubParam param = new SFOrderDetailSubParam();
        param.setOrderNo(order.getOrderCode());
        param.setCommunityMdmCode(communityCode);
        param.setSource(sfSource);
        param.setSubOrderNo(subOrderCode);
        SFOrderDetailSubResp subOrderDetailInfo = thirdServerClient.getSubOrderDetailInfo(param);
        if (ObjectUtils.isEmpty(subOrderDetailInfo)) {
            throw new BusinessException(CommonEnum.SYSTEM_ERROR);
        }
        if (!"0".equals(subOrderDetailInfo.getCode())) {
            throw new BusinessException(subOrderDetailInfo.getMessage(), CommonEnum.OTHER_ERROR.getCode());
        }
        // 为空则表示无数据，推送转佣订单
        if (ObjectUtils.isEmpty(subOrderDetailInfo.getContent())) {
            handleConvertCommissionOrder(orderId, subOrderCode);
        }

    }

    private void handleConvertCommissionOrder(Long orderId, String subOrderCode) {
        if (ObjectUtils.isEmpty(orderId) || ObjectUtils.isEmpty(subOrderCode)) {
            throw new BusinessException(CommonEnum.SYSTEM_ERROR);
        }
        modifyConvertCommissionOrderStatus(orderId, subOrderCode);

        List<TbBusinessSubOrderCommission> commissionOrders = commissionMapper.selectList(Wrappers.lambdaQuery(TbBusinessSubOrderCommission.class)
                .eq(TbBusinessSubOrderCommission::getSubOrderCode, subOrderCode)
                .eq(TbBusinessSubOrderCommission::getParentOrderId, orderId));
        List<TbBusinessSubOrderReceive> fromReceiveOrders = receiveMapper.selectBatchIds(commissionOrders.stream().map(TbBusinessSubOrderCommission::getExportSubOrderId).toList());
        List<Long> orderIdList = new ArrayList<>();
        orderIdList.add(orderId);
        Optional<Long> any = fromReceiveOrders.stream()
                .map(TbBusinessSubOrderReceive::getParentOrderId)
                .distinct()
                .findAny();
        any.ifPresent(orderIdList::add);
        Map<Long, TbBusinessOrder> orderIdMap = orderMapper.selectBatchIds(orderIdList)
                .stream()
                .collect(Collectors.toMap(TbBusinessOrder::getId, Function.identity(), (existing, next) -> existing));

        // 朝昔项目编码
        String communityCode = null;
        CustomerSourceResp customerSourceResp = null;
        String operatorName = null;
        String receiveCommunityId = null;
        String receiveCommunityName = null;
        String transactionContractNum = null;
        Optional<Long> commissionAny = commissionOrders.stream()
                .map(TbBusinessSubOrderCommission::getBusinessId)
                .findAny();
        if (commissionAny.isPresent()) {
            TbBusinessReceiveCommissionPlan plan = receiveCommissionPlanMapper.selectById(commissionAny.get());
            TbBusinessTransactionReport report = reportMapper.selectById(plan.getReportId());
            CostItemInfoResp costItemInfoResp = dataServerClient.queryOne(plan.getCostItemCode(), null);
            receiveCommunityId = plan.getCostItemId();
            receiveCommunityName = costItemInfoResp.getSfCostItemName();
            transactionContractNum = report.getTransactionContractNum();
            customerSourceResp = dataServerClient.innerQueryCustomerByIdList(List.of(report.getCustomerId()))
                    .stream()
                    .collect(Collectors.toMap(CustomerSourceResp::getId, Function.identity(), (existing, next) -> existing))
                    .get(report.getCustomerId());

            UserDetailsMicroResp userDetailsMicroResp = authorServerClient.getUserDetailsMicro(List.of(report.getBrokerId()))
                    .stream()
                    .collect(Collectors.toMap(UserDetailsMicroResp::getId, Function.identity(), (existing, next) -> existing))
                    .get(report.getBrokerId());
            if (!ObjectUtils.isEmpty(userDetailsMicroResp)) {
                operatorName = userDetailsMicroResp.getEmpName();
            }
            if (!ObjectUtils.isEmpty(report.getHouseId())) {    // 二手房 获取 项目 Code
                Long communityId = dataServerClient.listHouseInfoByIds(List.of(report.getHouseId()))
                        .stream()
                        .collect(Collectors.toMap(HouseInfoVo::getId, HouseInfoVo::getCommunityId, (existing, next) -> existing))
                        .get(report.getHouseId());
                CommunityResp communityResp = dataServerClient.getCommunityListByIds(List.of(communityId))
                        .stream()
                        .collect(Collectors.toMap(CommunityResp::getId, Function.identity(), (existing, next) -> existing))
                        .get(communityId);
                if (!ObjectUtils.isEmpty(communityResp)) {
                    communityCode = communityResp.getCommunityCode();
                }
            } else if (!ObjectUtils.isEmpty(report.getParkingSpaceId())) {  // 车位 获取 项目code
                ParkingSpaceResp parkingSpaceResp = dataServerClient.getParkingSpaceByIdList(List.of(report.getParkingSpaceId()))
                        .stream()
                        .collect(Collectors.toMap(ParkingSpaceResp::getId, Function.identity(), (existing, next) -> existing))
                        .get(report.getParkingSpaceId());
                if (!ObjectUtils.isEmpty(parkingSpaceResp)) {
                    CommunityResp communityResp = dataServerClient.getCommunityListByIds(List.of(parkingSpaceResp.getCommunityId()))
                            .stream()
                            .collect(Collectors.toMap(CommunityResp::getId, Function.identity(), (existing, next) -> existing))
                            .get(parkingSpaceResp.getCommunityId());
                    if (!ObjectUtils.isEmpty(communityResp)) {
                        communityCode = communityResp.getCommunityCode();
                    }
                }
            } else if (!ObjectUtils.isEmpty(report.getNewHouseCommunityId())) { // 新房项目获取 项目code
                NewHouseResp newHouseResp = dataServerClient.getNewHouseByIdList(List.of(report.getNewHouseCommunityId()))
                        .stream()
                        .collect(Collectors.toMap(NewHouseResp::getId, Function.identity(), (existing, next) -> existing))
                        .get(report.getNewHouseCommunityId());
                if (!ObjectUtils.isEmpty(newHouseResp)) {
                    communityCode = newHouseResp.getCommunityCode();
                }
            }
        }

        TbBusinessOrder fromOrder = null;
        if (any.isPresent()) {
            fromOrder = orderIdMap.get(any.get());
        }
        SFOrderAdjustParam adjustParam = generateConvertCommissionParam(orderIdMap.get(orderId),
                commissionOrders,
                ObjectUtils.isEmpty(fromOrder) ? null : fromOrder.getOrderCode(),
                communityCode,
                customerSourceResp,
                operatorName,
                receiveCommunityId,
                receiveCommunityName,
                transactionContractNum);
        SFOrderResp sfOrderResp = thirdServerClient.adjustToOrder(adjustParam);
        if (ObjectUtils.isEmpty(sfOrderResp)) {
            throw new BusinessException(CommonEnum.SYSTEM_ERROR);
        }
        if (!"0".equals(sfOrderResp.getCode())) {
            throw new BusinessException(sfOrderResp.getMessage(), CommonEnum.OTHER_ERROR.getCode());
        }
    }


    /**
     * 调用收费系统同步订单接口，同步订单
     * 如果收费系统存在主单，则改为新增子订单
     *
     * POS 支付时，会调用该函数推送收费系统，发起 POS 支付
     * 微信支付、支付宝支付时，完成支付，处理对应回调时，调用该函数，推送已支付订单到 收费系统
     *
     * @param order
     * @param orderReceive
     * @param payed 是否已支付
     * @param transactionId 收费系统支付单号  payed 为 true 时必传
     */
    private void pushOrderToSF(TbBusinessOrder order,
                               TbBusinessSubOrderReceive orderReceive,
                               Boolean payed, String transactionId) {
        log.info("开始推送订单到收费系统: 主订单号：{} 子订单号:{} 是否已支付:{} 收费系统支付单号（已支付时传送）:{}", order.getOrderCode(), orderReceive.getSubOrderCode(), payed, transactionId);
        // 朝昔项目编码
        String communityCode = null;
        List<Long> houseIdList = new LinkedList<>();
        List<Long> parkingSpaceIdList = new LinkedList<>();
        List<Long> newHouseCommunityIdList = new LinkedList<>();
        CustomerSourceResp customerSourceResp;
        Long operatorId;
        if (DropdownEnum.ORDER_SOURCE_TYPE_YJ.getDictKey().equals(order.getTransactionType())) {
            TbBusinessTransactionReport reports = getTransactionReportByOrderIdAndSubOrderId(order.getId(), orderReceive.getId());

            operatorId = reports.getCreateUser();
            if (!ObjectUtils.isEmpty(reports.getHouseId())) {
                houseIdList = List.of(reports.getHouseId());
            }
            if (!ObjectUtils.isEmpty(reports.getParkingSpaceId())) {
                parkingSpaceIdList = List.of(reports.getParkingSpaceId());
            }
            if (!ObjectUtils.isEmpty(reports.getNewHouseCommunityId())) {
                newHouseCommunityIdList = List.of(reports.getNewHouseCommunityId());
            }
            customerSourceResp = dataServerClient.innerQueryCustomerByIdList(List.of(reports.getCustomerId()))
                    .stream()
                    .collect(Collectors.toMap(CustomerSourceResp::getId, Function.identity(), (existing, next) -> existing))
                    .get(reports.getCustomerId());

        }else {
            TbBusinessDeposit deposits = getDepositBySubOrderId(orderReceive.getId());

            operatorId = deposits.getCreateUser();
            if (!ObjectUtils.isEmpty(deposits.getHouseDesc())) {
                houseIdList = Arrays.stream(deposits.getHouseDesc().split(RsmsConstant.SPLIT_COMMA)).map(Long::valueOf).toList();
            }
            if (!ObjectUtils.isEmpty(deposits.getParkingSpaceDesc())) {
                parkingSpaceIdList = Arrays.stream(deposits.getParkingSpaceDesc().split(RsmsConstant.SPLIT_COMMA)).map(Long::valueOf).toList();
            }
            customerSourceResp = dataServerClient.innerQueryCustomerByIdList(List.of(deposits.getCustomerId()))
                    .stream()
                    .collect(Collectors.toMap(CustomerSourceResp::getId, Function.identity(), (existing, next) -> existing))
                    .get(deposits.getCustomerId());
        }
        // 创建人即为操作人
        String operatorName = authorServerClient.getUserDetailsMicro(List.of(operatorId))
                .stream()
                .collect(Collectors.toMap(UserDetailsMicroResp::getId, UserDetailsMicroResp::getEmpName))
                .get(operatorId);
        List<Long> communityIdList = null;
        if (!ObjectUtils.isEmpty(houseIdList)) {
            communityIdList = dataServerClient.listHouseInfoByIds(houseIdList).stream()
                    .map(HouseInfoVo::getCommunityId)
                    .toList();
        } else if (!ObjectUtils.isEmpty(parkingSpaceIdList)) {
            communityIdList = dataServerClient.getParkingSpaceByIdList(parkingSpaceIdList).stream()
                    .map(ParkingSpaceResp::getCommunityId)
                    .toList();
        }
        if (!ObjectUtils.isEmpty(communityIdList)) {
            Optional<CommunityResp> any = dataServerClient.getCommunityListByIds(communityIdList)
                    .stream()
                    .findAny();
            if (any.isPresent()) {
                communityCode = any.get().getCommunityCode();
            }
        } else if (!ObjectUtils.isEmpty(newHouseCommunityIdList)){    // 新房项目获取 项目Code
            Optional<NewHouseResp> any = dataServerClient.getNewHouseByIdList(newHouseCommunityIdList)
                    .stream()
                    .findAny();
            if (any.isPresent()) {
                communityCode = any.get().getCommunityCode();
            }
        }
        SFOrderDetailResp orderDetailInfo = querySFOrderDetail(order.getOrderCode(), communityCode);
        // content 为空则表示订单不存在，做同步订单操作
        if (ObjectUtils.isEmpty(orderDetailInfo.getContent())) {
            SFOrderSyncParam sfOrderSyncParam = generateSyncParam(order, orderReceive, communityCode, customerSourceResp, operatorName, payed, transactionId);
            log.info("不存在订单，做同步订单操作：{}", JacksonUtil.toJSON(sfOrderSyncParam));
            SFOrderResp sfOrderResp = thirdServerClient.syncOrder(sfOrderSyncParam);
            log.info("同步订单结果：{}", JacksonUtil.toJSON(sfOrderResp));
            if (ObjectUtils.isEmpty(sfOrderResp)) {
                throw new BusinessException(CommonEnum.SYSTEM_ERROR);
            }
            if (!"0".equals(sfOrderResp.getCode())) {
                throw new BusinessException(sfOrderResp.getMessage(), CommonEnum.OTHER_ERROR.getCode());
            }
        }else { // 不为空，则做新增子订单操作
            // 检查是否存在当前子订单，不存在则推送，存在则跳过
            List<String> sfExistOrder = orderDetailInfo.getContent()
                    .getPays()
                    .stream()
                    .map(SFOrderDetailPayResp::getSubOrderNo)
                    .toList();
            if (!sfExistOrder.contains(orderReceive.getSubOrderCode())) {
                if (!ObjectUtils.isEmpty(orderDetailInfo.getContent().getPays())) {
                    List<String> list = orderDetailInfo.getContent().getPays()
                            .stream()
                            .filter(Objects::nonNull)
                            .map(SFOrderDetailPayResp::getSubOrderNo)
                            .toList();
                    if (!ObjectUtils.isEmpty(list) && list.contains(orderReceive.getSubOrderCode())) {
                        throw new BusinessException(BusinessEnum.ORDER_STATUS_ERROR);
                    }
                }
                SFOrderAddSubParam sfOrderAddSubParam = generateSubOrderParam(order, orderReceive, communityCode, customerSourceResp, operatorName, payed, transactionId);
                log.info("存在主订单，做新增子订单操作：{}", JacksonUtil.toJSON(sfOrderAddSubParam));
                SFOrderResp sfOrderResp = thirdServerClient.addSubOrder(sfOrderAddSubParam);
                log.info("新增子订单结果：{}", JacksonUtil.toJSON(sfOrderResp));
                if (ObjectUtils.isEmpty(sfOrderResp)) {
                    throw new BusinessException(CommonEnum.SYSTEM_ERROR);
                }
                if (!"0".equals(sfOrderResp.getCode())) {
                    throw new BusinessException(sfOrderResp.getMessage(), CommonEnum.OTHER_ERROR.getCode());
                }
            }
        }
        log.info("推送订单到收费系统完成......");
    }

    /**
     * 收费系统 订单查询
     * @param orderCode
     * @param communityCode
     */
    private SFOrderDetailResp querySFOrderDetail(String orderCode, String communityCode) {
        // 调用收费系统接口，查询主订单是否存在
        SFOrderDetailParam detailParam = new SFOrderDetailParam();
        detailParam.setOrderNo(orderCode);
        detailParam.setSource(sfSource);
        detailParam.setQueryPay(true);
        detailParam.setQueryRefund(true);
        detailParam.setQueryAdjust(true);

        detailParam.setCommunityMdmCode(communityCode);
        log.info("开始发起订单信息查询：{}", JacksonUtil.toJSON(detailParam));
        SFOrderDetailResp orderDetailInfo = thirdServerClient.getOrderDetailInfo(detailParam);
        log.info("订单信息查询结果：{}", JacksonUtil.toJSON(orderDetailInfo));
        if (ObjectUtils.isEmpty(orderDetailInfo)) {
            throw new BusinessException(CommonEnum.SYSTEM_ERROR);
        }

        if (!"0".equals(orderDetailInfo.getCode())) {
            throw new BusinessException(orderDetailInfo.getMessage(), CommonEnum.OTHER_ERROR.getCode());
        }
        return orderDetailInfo;
    }

    /**
     * 根据主订单ID  子订单ID 确定成交报告记录
     * @param orderId
     * @param subOrderId
     * @return
     */
    private TbBusinessTransactionReport getTransactionReportByOrderIdAndSubOrderId(Long orderId, Long subOrderId) {
        TbBusinessReceiveCommissionPlan commissionPlans = receiveCommissionPlanMapper.selectOne(Wrappers.lambdaQuery(TbBusinessReceiveCommissionPlan.class)
                .eq(TbBusinessReceiveCommissionPlan::getSubOrderId, subOrderId)
                .eq(TbBusinessReceiveCommissionPlan::getOrderId, orderId));
        return reportMapper.selectById(commissionPlans.getReportId());
    }

    /**
     * 根据子订单ID 确认意向金记录
     * @param subOrderId
     * @return
     */
    private TbBusinessDeposit getDepositBySubOrderId(Long subOrderId) {
        TbBusinessTransactionInfo transactionInfos = transactionInfoMapper.selectOne(Wrappers.lambdaQuery(TbBusinessTransactionInfo.class)
                .eq(TbBusinessTransactionInfo::getSubOrderId, subOrderId));
        return depositMapper.selectById(transactionInfos.getDepositId());
    }

    /**
     * 生成新增子订单参数
     *
     * @param order
     * @param orderReceive
     * @param communityCode
     * @param customerSourceResp
     * @param operatorName
     * @param payed
     * @return
     */
    private SFOrderAddSubParam generateSubOrderParam(TbBusinessOrder order,
                                                     TbBusinessSubOrderReceive orderReceive,
                                                     String communityCode,
                                                     CustomerSourceResp customerSourceResp,
                                                     String operatorName,
                                                     Boolean payed,
                                                     String transactionId) {
        SFOrderAddSubParam addSubParam = new SFOrderAddSubParam();
        addSubParam.setPayOrderNum(transactionId);
        addSubParam.setPayStatus(payed ? 1 : 0);
        addSubParam.setOrderNo(order.getOrderCode());
        addSubParam.setSource(sfSource);
        addSubParam.setCommunityMdmCode(communityCode);
        addSubParam.setOperatorName(operatorName);
        addSubParam.setCustomerId(customerSourceResp.getId());
        addSubParam.setCustomerName(customerSourceResp.getName());

        List<SFSubOrderAddParam> subOrderAddParams = Stream.of(orderReceive)
                .map(receive -> {
                    SFSubOrderAddParam subOrderAddParam = new SFSubOrderAddParam();
                    subOrderAddParam.setItemId(receive.getReceiveCommunityId());  // 收费项ID
                    subOrderAddParam.setItemName(receive.getReceiveCommunityName());
                    subOrderAddParam.setSubOrderNo(receive.getSubOrderCode());
                    subOrderAddParam.setOriginalAmount(receive.getPayAmount());
                    subOrderAddParam.setGoodsName(receive.getGoodName());
                    // 已支付金额（添加已支付订单时需要传）
                    if (payed) {
                        subOrderAddParam.setPayAmount(receive.getPayAmount());
                    }
                    return subOrderAddParam;
                }).toList();
        addSubParam.setSubOrderAmounts(subOrderAddParams);
        BigDecimal reduce = Stream.of(orderReceive)
                .map(TbBusinessSubOrderReceive::getPayAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        addSubParam.setAmount(reduce);
        return addSubParam;
    }

    /**
     * 生成转佣订单时，可能不存在主订单
     * @param order
     * @param commissions
     * @param communityCode
     * @param customerSourceResp
     * @return
     */
    private SFOrderAdjustParam generateConvertCommissionParam(TbBusinessOrder order,
                                                              List<TbBusinessSubOrderCommission> commissions,
                                                              String fromOrderCode,
                                                              String communityCode,
                                                              CustomerSourceResp customerSourceResp,
                                                              String operatorName,
                                                              String receiveCommunityId,
                                                              String receiveCommunityName,
                                                              String transactionContractNum) {
        BigDecimal amount = commissions.stream()
                .map(TbBusinessSubOrderCommission::getExportAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        SFOrderAdjustParam adjustParam = new SFOrderAdjustParam();
        adjustParam.setOrderNo(order.getOrderCode());
        adjustParam.setSource(sfSource);
        adjustParam.setCommunityMdmCode(communityCode);
        adjustParam.setOperatorName(operatorName);
        // 转出订单信息
        SFSubOrderAdjust fromSubOrder = new SFSubOrderAdjust();
        fromSubOrder.setOrderNo(fromOrderCode);
        fromSubOrder.setAmount(amount);
        // 转出子订单信息
        fromSubOrder.setSubOrderAmounts(commissions.stream()
                .map(commission -> {
                    SFSubOrderAmount fromSubOrderAmount = new SFSubOrderAmount();
                    fromSubOrderAmount.setAmount(commission.getExportAmount());
                    fromSubOrderAmount.setSubOrderNo(commission.getExportSubOrderCode());
                    return fromSubOrderAmount;
                }).toList());
        adjustParam.setFromSubOrders(fromSubOrder);

        // 转入订单信息
        SFSubOrderAdjust toSubOrder = new SFSubOrderAdjust();
        toSubOrder.setOrderNo(order.getOrderCode());
        // 转出金额即为转入金额
        toSubOrder.setAmount(amount);
        if (!ObjectUtils.isEmpty(customerSourceResp)) {
            toSubOrder.setCustomerId(customerSourceResp.getId());
            toSubOrder.setCustomerName(customerSourceResp.getName());
        }
        toSubOrder.setSecondClassificationId("601");
        toSubOrder.setGoodsName(DropdownEnum.ORDER_SOURCE_TYPE_YJ.getDictValue());
        toSubOrder.setContractNo(transactionContractNum);
        // 转入子订单信息
        SFSubOrderAmount toSubOrderAmount = new SFSubOrderAmount();
        toSubOrderAmount.setItemId(receiveCommunityId);
        toSubOrderAmount.setItemName(receiveCommunityName);
        Optional<String> any = commissions.stream()
                .map(TbBusinessSubOrderCommission::getSubOrderCode)
                .findAny();
        any.ifPresent(toSubOrderAmount::setSubOrderNo);
        toSubOrderAmount.setAmount(amount);
        toSubOrderAmount.setGoodsName(DropdownEnum.ORDER_SOURCE_TYPE_YJ.getDictValue());
        toSubOrder.setSubOrderAmounts(List.of(toSubOrderAmount));
        adjustParam.setToSubOrders(toSubOrder);
        return adjustParam;
    }

    /**
     * 生成同步订单参数
     * @param order
     * @param orderReceive
     * @param communityCode
     * @param customerSourceResp
     * @return
     */
    private SFOrderSyncParam generateSyncParam(TbBusinessOrder order,
                                               TbBusinessSubOrderReceive orderReceive,
                                               String communityCode,
                                               CustomerSourceResp customerSourceResp,
                                               String operatorName,
                                               Boolean payed,
                                               String transactionId){
        SFOrderSyncParam orderSyncParam = new SFOrderSyncParam();
        orderSyncParam.setOrderNo(order.getOrderCode());
        orderSyncParam.setSource(sfSource);
        orderSyncParam.setCommunityMdmCode(communityCode);
        orderSyncParam.setSecondClassificationId(DropdownEnum.ORDER_SOURCE_TYPE_YJ.getDictKey().equals(order.getTransactionType()) ? "601" : "600");
        orderSyncParam.setCustomerId(customerSourceResp.getId());
        orderSyncParam.setCustomerName(customerSourceResp.getName());
        orderSyncParam.setOperatorName(operatorName);
        String goodName;
        if (DropdownEnum.ORDER_SOURCE_TYPE_YJ.getDictKey().equals(order.getTransactionType())) {
            goodName = DropdownEnum.ORDER_SOURCE_TYPE_YJ.getDictValue();
        } else if (DropdownEnum.ORDER_SOURCE_TYPE_YXJ.getDictKey().equals(order.getTransactionType())) {
            goodName = DropdownEnum.ORDER_SOURCE_TYPE_YXJ.getDictValue();
        }else {
            goodName = DropdownEnum.ORDER_SOURCE_TYPE_DJ.getDictValue();
        }
        orderSyncParam.setGoodsName(goodName);
        orderSyncParam.setOriginalAmount(orderReceive.getPayAmount());
        orderSyncParam.setPayMode(Integer.valueOf(orderReceive.getPayMode()));
        // 已支付为 1 ，未支付为 0
        orderSyncParam.setPayStatus(payed ? 1 : 0);
        // 已支付金额（添加已支付订单时需要传）
        if (payed) {
            orderSyncParam.setPayAmount(orderReceive.getPayAmount());
            orderSyncParam.setPayOrderNum(transactionId);
        }
        orderSyncParam.setOrderTime(order.getCreateTime());
        orderSyncParam.setContractNo(order.getContractNum());

        SFSubOrderSyncParam subOrderSyncParam = new SFSubOrderSyncParam();
        subOrderSyncParam.setItemId(orderReceive.getReceiveCommunityId());
        subOrderSyncParam.setItemName(orderReceive.getReceiveCommunityName());
        subOrderSyncParam.setSubOrderNo(orderReceive.getSubOrderCode());
        subOrderSyncParam.setOriginalAmount(orderReceive.getPayAmount());
        subOrderSyncParam.setGoodsName(orderReceive.getGoodName());
        // 已支付金额（添加已支付订单时需要传）
        if (payed) {
            subOrderSyncParam.setPayAmount(orderReceive.getPayAmount());
        }
        orderSyncParam.setSubOrders(List.of(subOrderSyncParam));
        return orderSyncParam;
    }

    /**
     * 修改收款订单状态、业务数据状态
     * @param orderId
     * @param subOrderId
     * @param transactionType
     * @param payChannel
     */
    private void modifyPayOrderStatus(Long orderId, Long subOrderId, String transactionType, String payChannel) {
        orderMapper.update(Wrappers.lambdaUpdate(TbBusinessOrder.class)
                .eq(TbBusinessOrder::getId, orderId)
                .set(TbBusinessOrder::getStatus, DropdownEnum.ORDER_STATUS_PAYING.getDictKey()));
        receiveMapper.update(Wrappers.lambdaUpdate(TbBusinessSubOrderReceive.class)
                .eq(TbBusinessSubOrderReceive::getId, subOrderId)
                .eq(TbBusinessSubOrderReceive::getParentOrderId, orderId)
                .set(TbBusinessSubOrderReceive::getOrderStatus, DropdownEnum.ORDER_STATUS_PAYING.getDictKey())
                .set(TbBusinessSubOrderReceive::getPayChannel, payChannel));
        // 业务数据表 订单状态数据改变
        if (DropdownEnum.ORDER_SOURCE_TYPE_YJ.getDictKey().equals(transactionType)) {
            // 成交报告-收佣计划行 存在主、子订单ID，故根据 主、子订单ID 更新状态
            receiveCommissionPlanMapper.update(Wrappers.lambdaUpdate(TbBusinessReceiveCommissionPlan.class)
                    .eq(TbBusinessReceiveCommissionPlan::getOrderId, orderId)
                    .eq(TbBusinessReceiveCommissionPlan::getSubOrderId, subOrderId)
                    .set(TbBusinessReceiveCommissionPlan::getOrderStatus, DropdownEnum.TRANSFER_RECEIVE_PAYING.getDictKey()));
        }else {
            // 意向金-交易明细行 不存在主订单ID,故根据 子订单ID 更新状态即可
            transactionInfoMapper.update(Wrappers.lambdaUpdate(TbBusinessTransactionInfo.class)
                    .eq(TbBusinessTransactionInfo::getSubOrderId, subOrderId)
                    .set(TbBusinessTransactionInfo::getOrderStatus, DropdownEnum.TRANSACTION_ORDER_STATUS_PAYING.getDictKey()));
        }
    }

    /**
     * 修改转佣订单状态、业务数据状态
     * @param orderId
     * @param subOrderCode
     */
    private void modifyConvertCommissionOrderStatus(Long orderId, String subOrderCode) {
        orderMapper.update(Wrappers.lambdaUpdate(TbBusinessOrder.class)
                .eq(TbBusinessOrder::getId, orderId)
                .set(TbBusinessOrder::getStatus, DropdownEnum.ORDER_STATUS_CONVERT.getDictKey()));
        commissionMapper.update(Wrappers.lambdaUpdate(TbBusinessSubOrderCommission.class)
                .eq(TbBusinessSubOrderCommission::getSubOrderCode, subOrderCode)
                .eq(TbBusinessSubOrderCommission::getParentOrderId, orderId)
                .set(TbBusinessSubOrderCommission::getOrderStatus, DropdownEnum.ORDER_STATUS_CONVERT.getDictKey()));

        // 成交报告-收佣计划行 存在主、子订单ID
        // 一笔意向金只会存在一条收款-交易信息行，只会对应一条成交报告-转佣的 收佣行
        receiveCommissionPlanMapper.update(Wrappers.lambdaUpdate(TbBusinessReceiveCommissionPlan.class)
                .eq(TbBusinessReceiveCommissionPlan::getOrderId, orderId)
                .eq(TbBusinessReceiveCommissionPlan::getSubOrderCode, subOrderCode)
                .set(TbBusinessReceiveCommissionPlan::getOrderStatus, DropdownEnum.TRANSFER_RECEIVE_CONVERT.getDictKey()));
        // 意向金收款行也要关联
        TbBusinessReceiveCommissionPlan commissionPlan = receiveCommissionPlanMapper.selectOne(Wrappers.lambdaQuery(TbBusinessReceiveCommissionPlan.class)
                .eq(TbBusinessReceiveCommissionPlan::getOrderId, orderId)
                .eq(TbBusinessReceiveCommissionPlan::getSubOrderCode, subOrderCode));
        TbBusinessTransactionReport report = reportMapper.selectById(commissionPlan.getReportId());
        if (!ObjectUtils.isEmpty(report.getDepositId())) {
            transactionInfoMapper.update(Wrappers.lambdaUpdate(TbBusinessTransactionInfo.class)
                    .eq(TbBusinessTransactionInfo::getDepositId, report.getDepositId())
                    .notIn(TbBusinessTransactionInfo::getTransactionType, List.of(
                            DropdownEnum.TRANSACTION_TYPE_T_TYPE_01.getDictKey(),
                            DropdownEnum.TRANSACTION_TYPE_T_TYPE_02.getDictKey(),
                            DropdownEnum.TRANSACTION_TYPE_T_TYPE_03.getDictKey()
                    ))
                    .set(TbBusinessTransactionInfo::getOrderStatus, DropdownEnum.TRANSACTION_ORDER_STATUS_COMMISSIONED.getDictKey()));
        }
    }

    @Override
    public Page<OrderResp> mobileQueryOrderByUserId(Integer pageNum, Integer pageSize,
                                                    String orderStatus,
                                                    String transactionType,
                                                    String customerNameKeyWord, String houseKeyWord, String parkingKeyWord, String mobileKeyWord) {
        // 权限
        List<String> permissionList = List.of(
                PermissionEnum.ORDER_MANAGER_ORDER_VIEW.getCode()
        );
        log.info("权限码 list = {}", permissionList);
        List<Long> permissionUserIdList = PermissionUtil.getUserIdListByPermissionCode(permissionList)
                .get(PermissionEnum.ORDER_MANAGER_ORDER_VIEW.getCode());
        log.info("权限码对应用户Id Map = {}", permissionUserIdList);
        if (ObjectUtils.isEmpty(permissionUserIdList) || permissionUserIdList.contains(PermissionIsAllValueEnum.DISABLE.getValue())) {
            throw new BusinessException(CommonEnum.NOT_PERMISSION_ERROR);
        }

        // 由于车位模块现在暂未实现，故这里只查询房源ID
        List<Long> businessIdList = new LinkedList<>();
        if (!ObjectUtils.isEmpty(houseKeyWord)) {
            businessIdList.addAll(dataServerClient.getHouseIdByCommunityKeyWord(houseKeyWord));
        }
        QueryWrapper<TbBusinessOrder> like = Wrappers.query(TbBusinessOrder.class)
                .eq(!ObjectUtils.isEmpty(orderStatus), "status", orderStatus)
                .in(!ObjectUtils.isEmpty(transactionType), "transaction_type", transactionType);
        like.nested(!ObjectUtils.isEmpty(customerNameKeyWord)
                        || !ObjectUtils.isEmpty(mobileKeyWord)
                        || !ObjectUtils.isEmpty(businessIdList)
                , nest -> {
                    if (!ObjectUtils.isEmpty(customerNameKeyWord)) {
                        nest.like("customer_name", customerNameKeyWord)
                                .or();
                    }
                    if (!ObjectUtils.isEmpty(mobileKeyWord)) {
                        nest.like("customer_mobile", mobileKeyWord)
                                .or();
                    }
                    if (!ObjectUtils.isEmpty(businessIdList)) {
                        nest.nested(i -> {
                            businessIdList.forEach(businessId -> {
                                i.like("house_ids", businessId)
                                        .or();
                            });
                            i.eq("id", 0);
                        }).or();
                    }
                    nest.eq("id", 0);
                });

        if (!permissionUserIdList.contains(PermissionIsAllValueEnum.ALL.getValue())) {
            like.nested(i -> {
                i.in("create_user", permissionUserIdList);
            });
        }
        like.orderByAsc("case status " +
                " when 'unpay' then 0 " +
                " when 'part_pay' then 1 " +
                " when 'payed' then 2 " +
                " else 3 end");
        like.orderByDesc("create_time");
        Page<TbBusinessOrder> tbBusinessOrderPage = new Page<>(pageNum, pageSize);
        Page<TbBusinessOrder> orderPage = orderMapper.selectPage(tbBusinessOrderPage, like);

        // 设置房源车位信息
        List<Long> houseIdList = orderPage.getRecords().stream()
                .map(TbBusinessOrder::getHouseIds)
                .filter(Objects::nonNull)
                .map(houseIds -> Arrays.stream(houseIds.split(RsmsConstant.SPLIT_COMMA)).toList())
                .flatMap(Collection::stream)
                .map(Long::valueOf)
                .distinct()
                .toList();
        Map<Long, HouseInfoVo> houseInfoVoMap = queryHouseInfoMap(houseIdList);

        List<Long> parkingSpaceIdList = orderPage.getRecords().stream()
                .map(TbBusinessOrder::getParkingIds)
                .filter(Objects::nonNull)
                .map(parkingSpaceIds -> Arrays.stream(parkingSpaceIds.split(RsmsConstant.SPLIT_COMMA)).toList())
                .flatMap(Collection::stream)
                .map(Long::valueOf)
                .distinct()
                .toList();
        Map<Long, ParkingSpaceResp> parkingSpaceRespMap = queryParkingSpaceInfoMap(parkingSpaceIdList);


        List<OrderResp> orderResps = orderPage.getRecords().stream()
                .map(order -> {
                    return generateOrderResp(order, houseInfoVoMap, parkingSpaceRespMap);
                })
                .toList();
        setSubOrderDetail(orderResps);
        Page<OrderResp> resultOrderRespPage = new Page<>(orderPage.getCurrent(), orderPage.getSize(), orderPage.getTotal());
        resultOrderRespPage.setRecords(orderResps);
        return resultOrderRespPage;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public SFCallBackResp payCallBackOnSF(SFOrderCallBackParam callBackParam) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.registerModule(new JavaTimeModule());
            log.info("收到支付回调！！！ 内容：{}", objectMapper.writeValueAsString(callBackParam));
            if (ObjectUtils.isEmpty(callBackParam)) {
                throw new BusinessException(CommonEnum.REQUEST_PARAMS_OBJ_NOT_NULL);
            }
            List<TbBusinessOrderCallback> list;
            // 线下转账 回调
            if (DropdownEnum.SF_PAY_CHANNEL_TRANSFER_OFFLINE.getDictKey().equals(callBackParam.getPayChannel())) {
                list = generateOrderCallbackOnTransferOffline(callBackParam);
            } else if (DropdownEnum.SF_PAY_CHANNEL_CMB.getDictKey().equals(callBackParam.getPayChannel())) {
                // 招行聚合支付
                list = generateOrderCallbackOnCMB(callBackParam);
            }else { // 其他类型均为聚合支付
                list = generateOrderCallbackOnPos(callBackParam);
            }
            if (!ObjectUtils.isEmpty(list)) {
                callbackMapper.insert(list);
                log.info("【收费系统】支付回调入库：{}", list);
                // 回调表 ID 丢到 MQ 中，由消费者进行订单状态的修改
                MessageContent<List<Long>> messageContent = new MessageContent<>();
                messageContent.setMsgId(UUID.randomUUID().toString());
                messageContent.setData(list.stream().map(TbBusinessOrderCallback::getId).toList());
                messageContent.setType(DropdownEnum.MQ_MESSAGE_TYPE_RECEIVE.getDictKey());
                messageContent.setCreateTime(new Date());
                String s = objectMapper.writeValueAsString(messageContent);
                sendMsg.sendChargeOrderMsg(s);
            }
            return SFCallBackResp.success();
        } catch (Exception e) {
            log.error("【收费系统】支付回调处理异常，回调信息：{}，异常信息：{}", callBackParam, e.getMessage());
            return SFCallBackResp.fail();
        }
    }

    /**
     * 根据回调生成 订单回调记录  仅适用于 招行聚合支付 支付渠道回调
     * @param callBackParam
     * @return
     */
    private List<TbBusinessOrderCallback> generateOrderCallbackOnCMB(SFOrderCallBackParam callBackParam) {
        TbBusinessOrderCallback callback = new TbBusinessOrderCallback();
        callback.setId(snowFlake.nextId());
        callback.setOutTradeNo(callBackParam.getOutTradeNo());
        callback.setStatus(callBackParam.getTradeState());
        callback.setBody(JacksonUtil.toJSON(callBackParam));
        return List.of(callback);
    }

    /**
     * 根据回调生成 订单回调记录  仅适用于 线下转账 支付渠道回调
     * @param callBackParam
     * @return
     */
    private List<TbBusinessOrderCallback> generateOrderCallbackOnTransferOffline(SFOrderCallBackParam callBackParam){
        List<TbBusinessOrderCallback> tbBusinessOrderCallbacks = analysisSubItems(callBackParam.getSubItems());
        tbBusinessOrderCallbacks.forEach(tbBusinessOrderCallback -> {
            tbBusinessOrderCallback.setStatus(callBackParam.getTradeState());
            tbBusinessOrderCallback.setBody(JacksonUtil.toJSON(callBackParam));
        });
        return tbBusinessOrderCallbacks;
    }


    /**
     * 根据回调生成 订单回调记录  仅适用于 POS 支付渠道回调
     * @param callBackParam
     * @return
     */
    private List<TbBusinessOrderCallback> generateOrderCallbackOnPos(SFOrderCallBackParam callBackParam){
        List<TbBusinessOrderCallback> tbBusinessOrderCallbacks = analysisSubItems(callBackParam.getSubItems());
        tbBusinessOrderCallbacks.forEach(tbBusinessOrderCallback -> {
            tbBusinessOrderCallback.setStatus(callBackParam.getTradeState());
            tbBusinessOrderCallback.setBody(JacksonUtil.toJSON(callBackParam));
        });
        return tbBusinessOrderCallbacks;
    }

    /**
     * 根据回调 subItem 属性 生成回调记录
     * @param subItems
     * @return
     */
    private List<TbBusinessOrderCallback> analysisSubItems(List<SFSubOrderCallBackParam> subItems) {
        if (ObjectUtils.isEmpty(subItems)) {
            return new LinkedList<>();
        }
        return subItems
                .stream()
                .filter(i -> {
                    // 幂等性检查
                    boolean empty = callbackMapper.selectList(Wrappers.lambdaQuery(TbBusinessOrderCallback.class)
                                    .eq(TbBusinessOrderCallback::getOutTradeNo, i.getOutOrderNo())
                                    .eq(TbBusinessOrderCallback::getSubOutTradeNo, i.getSubOutOrderNo()))
                            .stream()
                            .findAny().isEmpty();
                    if (!empty) {
                        log.info("主订单号：{} 子订单号：{} 本次通知子订单：{} ；通知已入库，本次通知子订单不入库。", i.getOutOrderNo(), i.getSubOutOrderNo(), JacksonUtil.toJSON(i));
                    }
                    return empty;
                })
                .map(i -> {
                    // POS 支付 主流水号 =  主订单号、子流水号 = 子订单号，故确认一笔唯一的回调需要 主流水号 + 子订单号
                    TbBusinessOrderCallback callback = new TbBusinessOrderCallback();
                    callback.setId(snowFlake.nextId());
                    callback.setOutTradeNo(i.getOutOrderNo());
                    callback.setSubOutTradeNo(i.getSubOutOrderNo());
                    callback.setTotalFee(i.getSubTotalFee());
                    return callback;
                }).toList();
    }

    @Override
    public SFCallBackResp refundCallBackOnSF(SFOrderRefundCallBackParam refundCallBackParam) {
        try {
            log.info("收到退款回调！！！ 内容：{}", JacksonUtil.toJSON(refundCallBackParam));
            if (ObjectUtils.isEmpty(refundCallBackParam)) {
                throw new BusinessException(CommonEnum.REQUEST_PARAMS_OBJ_NOT_NULL);
            }

            TbBusinessOrderCallback callback = new TbBusinessOrderCallback();
            callback.setId(snowFlake.nextId());
            callback.setOutTradeNo(refundCallBackParam.getOutRefundNo());
            callback.setTotalFee(refundCallBackParam.getRefundAmount());
            callback.setStatus(refundCallBackParam.getRefundResult());
            callback.setBody(JacksonUtil.toJSON(refundCallBackParam));

            callbackMapper.insert(callback);
            log.info("【收费系统】退款回调入库：{}", callback);

            // TODO 回调表 ID 丢到 MQ 中，由消费者进行订单状态的修改
            MessageContent<List<Long>> messageContent = new MessageContent<>();
            messageContent.setMsgId(UUID.randomUUID().toString());
            messageContent.setData(List.of(callback.getId()));
            messageContent.setType(DropdownEnum.MQ_MESSAGE_TYPE_REFUND.getDictKey());
            messageContent.setCreateTime(new Date());
            String s = JacksonUtil.toJSON(messageContent);
            sendMsg.sendChargeOrderMsg(s);
            return SFCallBackResp.success();
        } catch (Exception e) {
            log.error("【收费系统】退款回调处理异常，回调信息：{}，异常信息：{}", refundCallBackParam, e.getMessage());
            return SFCallBackResp.fail();
        }
    }

    /**
     * 处理回调订单
     *
     * 1. 收款子订单
     *      修改 子订单状态 为 已付款
     *      修改 主订单状态 为 已付款
     *      修改 业务数据 状态
     *          YJ 类型订单：修改收佣计划表 对应的业务数据中的订单状态
     *                      判断对应的 成交报告 下所有的订单是否是已完结状态（已收款、已转佣）,根据判断结果改变 成交报告 状态
     *          YXJ、DJ 类型订单：修改意向金表 对应的业务数据的 状态
     *
     * @param orderCode 主订单编号
     * @param subOrderCode  子订单编号
     * @param status    状态：success、fail、close
     * @param payChannel 支付渠道
     * @param payFinishTime 支付完成时间
     * @param payAmount 支付金额
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handlePayCallBack(String orderCode, String subOrderCode, String status, String payChannel, LocalDateTime payFinishTime, BigDecimal payAmount) {
        log.info("开始处理订单回调~~~ 主订单号：{} 子订单号：{} 状态：{} 支付时间：{} 支付金额：{} ", orderCode, subOrderCode, status, payFinishTime, payAmount);
        payCallBackOnOrder(orderCode, subOrderCode, status, payChannel, payAmount);

        TbBusinessOrder order = orderMapper.selectOne(Wrappers.lambdaQuery(TbBusinessOrder.class).eq(TbBusinessOrder::getOrderCode, orderCode));
        // 成交报告订单
        if (DropdownEnum.ORDER_SOURCE_TYPE_YJ.getDictKey().equals(order.getTransactionType())) {
            payCallBackOnTransaction(orderCode, subOrderCode, status, payFinishTime);
        } else { // 意向金表 状态修改
            payCallBackOnDeposit(subOrderCode, status, payFinishTime);
        }
    }

    /**
     * 根据支付回调结果 修改订单数据状态
     *
     * @param orderCode
     * @param subOrderCode
     * @param payStatus
     */
    private void payCallBackOnOrder(String orderCode, String subOrderCode, String payStatus, String payChannel, BigDecimal payAmount) {
        // 存在非当前子订单的其他已收款的子订单或者 转佣子订单，则主订单状态回退到 部分收款，否则回退到待收款
        TbBusinessOrder order = orderMapper.selectOne(Wrappers.lambdaQuery(TbBusinessOrder.class)
                .eq(TbBusinessOrder::getOrderCode, orderCode));
        List<TbBusinessSubOrderCommission> orderCommissions = commissionMapper.selectList(Wrappers.lambdaQuery(TbBusinessSubOrderCommission.class)
                .eq(TbBusinessSubOrderCommission::getParentOrderId, order.getId()));
        List<TbBusinessSubOrderReceive> orderReceives = receiveMapper.selectList(Wrappers.lambdaQuery(TbBusinessSubOrderReceive.class)
                .eq(TbBusinessSubOrderReceive::getParentOrderId, order.getId()));
        List<TbBusinessSubOrderReceive> otherSubOrderList = orderReceives.stream()
                .filter(subOrder -> !subOrder.getSubOrderCode().equals(subOrderCode))
                .toList();
        String orderStatus = null, subOrderStatus = null;
        if (DropdownEnum.SF_ROLLBACK_STATUS_SUCCESS.getDictKey().equals(payStatus)) {
            orderStatus = DropdownEnum.ORDER_STATUS_PAYED.getDictKey();
            subOrderStatus = DropdownEnum.ORDER_STATUS_PAYED.getDictKey();
            // 如果本次子订单支付成功，且订单下还存在其他的待收款订单，则状态为 部分收款，否则为全部收款
            List<TbBusinessSubOrderReceive> unpayOrders = otherSubOrderList.stream()
                    .filter(receive -> (
                                    DropdownEnum.ORDER_STATUS_UNPAY.getDictKey().equals(receive.getOrderStatus())
                                            || DropdownEnum.ORDER_STATUS_PAYING.getDictKey().equals(receive.getOrderStatus())
                            )
                    ).toList();
            // 存在待支付、支付中状态下的子收款订单 则主状态变更为 部分收款
            if (!ObjectUtils.isEmpty(unpayOrders)) {
                orderStatus = DropdownEnum.ORDER_STATUS_PART_PAY.getDictKey();
            }
        } else if (DropdownEnum.SF_ROLLBACK_STATUS_FAIL.getDictKey().equals(payStatus)
                || DropdownEnum.SF_ROLLBACK_STATUS_CLOSE.getDictKey().equals(payStatus)
                || DropdownEnum.SF_ROLLBACK_STATUS_NOTPAY.getDictKey().equals(payStatus)) {
            orderStatus = DropdownEnum.ORDER_STATUS_UNPAY.getDictKey();
            subOrderStatus = DropdownEnum.ORDER_STATUS_UNPAY.getDictKey();
            // 存在已收款的子订单
            List<TbBusinessSubOrderReceive> receivedOrder = otherSubOrderList.stream()
                    .filter(receive -> DropdownEnum.ORDER_STATUS_PAYED.getDictKey().equals(receive.getOrderStatus()))
                    .toList();
            if (!ObjectUtils.isEmpty(receivedOrder)) {
                orderStatus = DropdownEnum.ORDER_STATUS_PART_PAY.getDictKey();
            }
            // 存在已转佣的子订单
            List<TbBusinessSubOrderCommission> convertCommissionOrder = orderCommissions.stream()
                    .filter(orderCommission -> DropdownEnum.ORDER_STATUS_CONVERT.getDictKey().equals(orderCommission.getOrderStatus()))
                    .toList();
            // 存在已转佣金 或者 已收款的子订单
            if (!ObjectUtils.isEmpty(convertCommissionOrder)) {
                orderStatus = DropdownEnum.ORDER_STATUS_CONVERT.getDictKey();
            }
        }
        if (!ObjectUtils.isEmpty(orderStatus) && !ObjectUtils.isEmpty(subOrderStatus)) {
            orderReceives.stream()
                    .filter(subOrder -> subOrder.getSubOrderCode().equals(subOrderCode))
                    .findFirst()
                    .ifPresent(consumer -> {
                        log.info("订单号：{} 子订单号：{} 原支付渠道：{} 原支付金额：{} , 收费系统返回实际支付渠道：{} 实际支付金额：{}",
                                orderCode, subOrderCode, consumer.getPayChannel(), consumer.getPayAmount(),
                                payChannel, payAmount);
                    });
            // 子订单状态修改为 已收款
            receiveMapper.update(Wrappers.lambdaUpdate(TbBusinessSubOrderReceive.class)
                    .eq(TbBusinessSubOrderReceive::getSubOrderCode, subOrderCode)
                    .set(TbBusinessSubOrderReceive::getOrderStatus, subOrderStatus)
                    .set(DropdownEnum.SF_ROLLBACK_STATUS_SUCCESS.getDictKey().equals(payStatus), TbBusinessSubOrderReceive::getPayChannel, payChannel)
                    .set(DropdownEnum.SF_ROLLBACK_STATUS_SUCCESS.getDictKey().equals(payStatus), TbBusinessSubOrderReceive::getPayAmount, payAmount));

            // 主订单状态改为 已收款
            orderMapper.update(Wrappers.lambdaUpdate(TbBusinessOrder.class)
                    .eq(TbBusinessOrder::getOrderCode, orderCode)
                    .set(TbBusinessOrder::getStatus, orderStatus));
        }
    }

    /**
     * 支付回调处理-成交报告
     * 进修改 收佣行中的订单状态，成交报告记录本身的状态不做变更
     * @param orderCode
     * @param subOrderCode
     * @param payStatus
     * @param payFinishTime
     */
    private void payCallBackOnTransaction(String orderCode, String subOrderCode, String payStatus, LocalDateTime payFinishTime) {
        TbBusinessOrder order = orderMapper.selectOne(Wrappers.lambdaQuery(TbBusinessOrder.class)
                .eq(TbBusinessOrder::getOrderCode, orderCode));
        TbBusinessSubOrderReceive orderReceive = receiveMapper.selectOne(Wrappers.lambdaQuery(TbBusinessSubOrderReceive.class)
                .eq(TbBusinessSubOrderReceive::getSubOrderCode, subOrderCode));

        // 先查询成交报告数据，再根据成交报告查询所有的主订单，如果均为已收款则记录状态修改为已收款
        TbBusinessReceiveCommissionPlan commissionPlan = receiveCommissionPlanMapper.selectOne(Wrappers.lambdaQuery(TbBusinessReceiveCommissionPlan.class)
                .eq(TbBusinessReceiveCommissionPlan::getOrderId, order.getId())
                .eq(TbBusinessReceiveCommissionPlan::getSubOrderId, orderReceive.getId()));
        List<TbBusinessOrder> orders = orderMapper.selectList(Wrappers.lambdaQuery(TbBusinessOrder.class)
                .in(TbBusinessOrder::getId, receiveCommissionPlanMapper.selectList(Wrappers.lambdaQuery(TbBusinessReceiveCommissionPlan.class)
                                .eq(TbBusinessReceiveCommissionPlan::getReportId, commissionPlan.getReportId()))
                        .stream()
                        .map(TbBusinessReceiveCommissionPlan::getOrderId)
                        .filter(Objects::nonNull)
                        .toList()
                )
        );
        // 查询出当前子订单外的其他子订单的状态，用来更新 合同的状态
        List<TbBusinessSubOrderReceive> otherReceiveList = receiveMapper.selectList(Wrappers.lambdaQuery(TbBusinessSubOrderReceive.class)
                .in(TbBusinessSubOrderReceive::getParentOrderId, orders.stream().map(TbBusinessOrder::getId).toList())
                .notIn(TbBusinessSubOrderReceive::getSubOrderCode, subOrderCode));

        String subOrderStatus = null;
        String contractStatus = null;
        if (DropdownEnum.SF_ROLLBACK_STATUS_SUCCESS.getDictKey().equals(payStatus)) {
            subOrderStatus = DropdownEnum.TRANSFER_RECEIVE_PAYED.getDictKey();

            // 成交报告下所有的主订单 均为已完结状态：已收款、已转佣
            // 当前子订单支付成功，该合同下所有子订单 均 已支付或已转佣，则为已收款
            boolean b = otherReceiveList.stream()
                    .allMatch(o -> {
                        return DropdownEnum.ORDER_STATUS_PAYED.getDictKey().equals(o.getOrderStatus())
                                || DropdownEnum.ORDER_STATUS_CONVERT.getDictKey().equals(o.getOrderStatus());
                    });
            if (b) {
                contractStatus = DropdownEnum.CONTRACT_STATUS_PAYMENT_FOR_ALL.getDictKey();
            } else { // 存在转佣中、收款中 的订单
                contractStatus = DropdownEnum.CONTRACT_STATUS_PAYMENT_FOR_PARTIAL.getDictKey();
            }
        } else if (DropdownEnum.SF_ROLLBACK_STATUS_FAIL.getDictKey().equals(payStatus)
                || DropdownEnum.SF_ROLLBACK_STATUS_NOTPAY.getDictKey().equals(payStatus)
                || DropdownEnum.SF_ROLLBACK_STATUS_CLOSE.getDictKey().equals(payStatus)) {
            subOrderStatus = DropdownEnum.TRANSFER_RECEIVE_UNPAY.getDictKey();
            // 当前订单回调返回失败\取消 且 成交报告关联的其他订单处于 已付款\部分付款\已转佣 则 成交报告状态回退到 部分收款；否则 回退到 待收款
            // 当前子订单支付失败，但是该合同下存在 已支付、已转佣的子订单，则回退到部分收款
            boolean b = otherReceiveList.stream()
                    .anyMatch(o -> {
                        return DropdownEnum.ORDER_STATUS_PAYED.getDictKey().equals(o.getOrderStatus())
                                || DropdownEnum.ORDER_STATUS_CONVERT.getDictKey().equals(o.getOrderStatus());
                    });
            if (b) {
                contractStatus = DropdownEnum.CONTRACT_STATUS_PAYMENT_FOR_PARTIAL.getDictKey();
            }else {
                contractStatus = DropdownEnum.CONTRACT_STATUS_GENERATED_REPORT.getDictKey();
            }
        }
        // 仅修改 收佣计划行、合同状态、不涉及 成交报告表状态
        // 收佣计划表 订单状态 修改
        receiveCommissionPlanMapper.update(Wrappers.lambdaUpdate(TbBusinessReceiveCommissionPlan.class)
                .eq(TbBusinessReceiveCommissionPlan::getOrderCode, orderCode)
                .eq(TbBusinessReceiveCommissionPlan::getSubOrderCode, subOrderCode)
                .set(TbBusinessReceiveCommissionPlan::getOrderStatus, subOrderStatus)
                .set((DropdownEnum.TRANSFER_RECEIVE_PAYED.getDictKey().equals(subOrderStatus)),TbBusinessReceiveCommissionPlan::getActualPayment, payFinishTime)
                .setSql((DropdownEnum.TRANSFER_RECEIVE_PAYED.getDictKey().equals(subOrderStatus)),
                        "received_commission = received_commission + %s".formatted(orderReceive.getPayAmount().toString()))
        );

        //更新合同状态
        TbBusinessTransactionReport report = reportMapper.selectById(commissionPlan.getReportId());
        if (!ObjectUtils.isEmpty(report.getTransactionContractNum())) {
            bizContractMapper.update(Wrappers.lambdaUpdate(TbBusinessContract.class)
                    .eq(TbBusinessContract::getContractNumber,report.getTransactionContractNum())
                    .set(TbBusinessContract::getContractStatus, contractStatus));
        }
    }
    /**
     * 根据支付回调结果 修改意向金 交易信息行的订单状态，不改动意向金本身的状态
     * @param subOrderCode
     * @param payStatus
     * @param payFinishTime
     */
    private void payCallBackOnDeposit(String subOrderCode, String payStatus, LocalDateTime payFinishTime) {
        String transactionInfoOrderStatus = null;
        // 收费回调返回支付成功
        if (DropdownEnum.SF_ROLLBACK_STATUS_SUCCESS.getDictKey().equals(payStatus)) {
            transactionInfoOrderStatus = DropdownEnum.TRANSACTION_ORDER_STATUS_PAYED.getDictKey();

        } else if (DropdownEnum.SF_ROLLBACK_STATUS_FAIL.getDictKey().equals(payStatus)
                || DropdownEnum.SF_ROLLBACK_STATUS_NOTPAY.getDictKey().equals(payStatus)
                || DropdownEnum.SF_ROLLBACK_STATUS_CLOSE.getDictKey().equals(payStatus)) {
            // 收费回调返回 支付失败、支付取消，业务数据状态回退到 待支付
            transactionInfoOrderStatus = DropdownEnum.TRANSACTION_ORDER_STATUS_UNPAY.getDictKey();
        }
        if (!ObjectUtils.isEmpty(transactionInfoOrderStatus)) {
            transactionInfoMapper.update(Wrappers.lambdaUpdate(TbBusinessTransactionInfo.class)
                    .eq(TbBusinessTransactionInfo::getSubOrderCode, subOrderCode)
                    .set(TbBusinessTransactionInfo::getOrderStatus, transactionInfoOrderStatus)
                    .set((DropdownEnum.SF_ROLLBACK_STATUS_SUCCESS.getDictKey().equals(payStatus)),
                            TbBusinessTransactionInfo::getTransactionDateTime, payFinishTime)
            );
        }
    }

    /**
     * 处理聚合支付回调
     * @param fromInnerTask 是否来源于内部轮询任务
     * @param transactionId 收费系统订单支付单号
     * @param outOrderNo 主订单流水号
     * @param subOutOrderNo 子订单流水号
     * @param status 收费系统返回的支付状态
     * @param payFinishTime 支付时间
     * @param payAmount 支付金额
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleThirdPayCallBack(Boolean fromInnerTask,
                                       String transactionId,
                                       String outOrderNo,
                                       String subOutOrderNo,
                                       String status,
                                       String payChannel,
                                       LocalDateTime payFinishTime,
                                       BigDecimal payAmount) {
        TbBusinessTradeOrderLog tradeOrderLog = tradeOrderLogService.getOne(Wrappers.lambdaQuery(TbBusinessTradeOrderLog.class)
                .eq(TbBusinessTradeOrderLog::getOrderSerialNumber, outOrderNo)
                .eq(!ObjectUtils.isEmpty(subOutOrderNo), TbBusinessTradeOrderLog::getSubOrderSerialNumber, subOutOrderNo)
                .in(TbBusinessTradeOrderLog::getStatus, List.of(
                        DropdownEnum.TRADE_ORDER_LOG_STATUS_SUCCESS.getDictKey(),
                        DropdownEnum.TRADE_ORDER_LOG_STATUS_MANUALLY_CANCEL.getDictKey(),
                        DropdownEnum.TRADE_ORDER_LOG_STATUS_SUCCESS_PAY.getDictKey(),
                        DropdownEnum.TRADE_ORDER_LOG_STATUS_FAIL_PAY.getDictKey(),
                        DropdownEnum.TRADE_ORDER_LOG_STATUS_CANCEL_PAY.getDictKey(),
                        DropdownEnum.TRADE_ORDER_LOG_STATUS_NOT_PAY.getDictKey()
                )));
        if (ObjectUtils.isEmpty(tradeOrderLog)) {
            log.info("处理第三方支付回调异常，对应下单流水记录：{}", tradeOrderLog);
            throw new BusinessException(BusinessEnum.ORDER_SERIAL_ERROR);
        }

        RLock lock = redissonClient.getLock(String.format(RedisConstant.TRADE_ORDER_SERIAL_NUMBER_PREFIX, tradeOrderLog.getOrderId()));
        try {
            lock.lock();
            // 检查流水记录状态
            if (List.of(
                    DropdownEnum.TRADE_ORDER_LOG_STATUS_SUCCESS_PAY.getDictKey(),
                    DropdownEnum.TRADE_ORDER_LOG_STATUS_FAIL_PAY.getDictKey(),
                    DropdownEnum.TRADE_ORDER_LOG_STATUS_CANCEL_PAY.getDictKey(),
                    DropdownEnum.TRADE_ORDER_LOG_STATUS_NOT_PAY.getDictKey()
            ).contains(tradeOrderLog.getStatus())) {
                log.info("流水号Id :{} 本次更新来源：{} ，已更新过 支付状态，记录更新标志：定时任务更新：{} ， 收费回调更新：{} 不再重复更新....",
                        tradeOrderLog.getId(), fromInnerTask ? "定时任务" : "收费回调", tradeOrderLog.getInnerTaskFlag(), tradeOrderLog.getCallBackFlag());
                return;
            }
            log.info("调用通用支付回调逻辑，处理业务数据状态, 主订单号：{} 子订单号：{}.....", tradeOrderLog.getOrderCode(), tradeOrderLog.getSubOrderCode());
            handlePayCallBack(tradeOrderLog.getOrderCode(), tradeOrderLog.getSubOrderCode(), status, payChannel, payFinishTime, payAmount);
            // 支付成功时，才向收费系统推送订单
            if (DropdownEnum.SF_ROLLBACK_STATUS_SUCCESS.getDictKey().equals(status)) {
                TbBusinessOrder order = orderMapper.selectById(tradeOrderLog.getOrderId());
                TbBusinessSubOrderReceive orderReceive = receiveMapper.selectById(tradeOrderLog.getSubOrderId());
                log.info("聚合支付结果返回成功支付，开始向收费系统推送订单，用以同步订单数据：主订单数据：{} 子订单数据：{}", order, orderReceive);
                // 向收费系统推送订单
                pushOrderToSF(order, orderReceive, true, transactionId);
            }

            log.info("变更流水表对应支付流水的状态：流水表ID: {} 状态：{}", tradeOrderLog.getId(), status);
            // 流水表状态更新
            syncTradeOrderSerialStatus(fromInnerTask, tradeOrderLog.getId(), status);
        }catch (Exception e){
            log.error("处理聚合支付回调异常....", e);
            throw e;
        }finally {
            lock.unlock();
        }
    }

    /**
     * 流水表状态更新
     * @param fromInnerTask 是否来自内部定时任务[主动发起查询也属于内部定时任务] true-是，false-来自收费回调
     * @param serialNumberId
     * @param tradeOrderStatus
     */
    private void syncTradeOrderSerialStatus(Boolean fromInnerTask, Long serialNumberId, String tradeOrderStatus) {
        String serialStatus = null;
        if (DropdownEnum.SF_ROLLBACK_STATUS_SUCCESS.getDictKey().equals(tradeOrderStatus)) {
            serialStatus = DropdownEnum.TRADE_ORDER_LOG_STATUS_SUCCESS_PAY.getDictKey();
        } else if (DropdownEnum.SF_ROLLBACK_STATUS_FAIL.getDictKey().equals(tradeOrderStatus)) {
            serialStatus = DropdownEnum.TRADE_ORDER_LOG_STATUS_FAIL_PAY.getDictKey();
        } else if (DropdownEnum.SF_ROLLBACK_STATUS_CLOSE.getDictKey().equals(tradeOrderStatus)) {
            serialStatus = DropdownEnum.TRADE_ORDER_LOG_STATUS_CANCEL_PAY.getDictKey();
        } else if (DropdownEnum.SF_ROLLBACK_STATUS_NOTPAY.getDictKey().equals(tradeOrderStatus)) {
            serialStatus = DropdownEnum.TRADE_ORDER_LOG_STATUS_NOT_PAY.getDictKey();
        }
        if (!ObjectUtils.isEmpty(serialStatus)) {
            tradeOrderLogService.update(Wrappers.lambdaUpdate(TbBusinessTradeOrderLog.class)
                    .eq(TbBusinessTradeOrderLog::getId, serialNumberId)
                    .eq(TbBusinessTradeOrderLog::getStatus, DropdownEnum.TRADE_ORDER_LOG_STATUS_SUCCESS.getDictKey())
                    .set(fromInnerTask, TbBusinessTradeOrderLog::getInnerTaskFlag, RsmsConstant.FLAG_YES)
                    .set(!fromInnerTask, TbBusinessTradeOrderLog::getCallBackFlag, RsmsConstant.FLAG_YES)
                    .set(TbBusinessTradeOrderLog::getStatus, serialStatus));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleRefundCallBack(String refundCode, String status, LocalDateTime refundFinishTime, BigDecimal refundAmount) {
        log.info("开始处理订单退款回调~~~ 退款订单号：{} 状态：{} 退款时间：{} 退款金额：{} ", refundAmount, status, refundFinishTime, refundAmount);
        TbBusinessSubOrderRefund refundOrder = refundMapper.selectOne(Wrappers.lambdaQuery(TbBusinessSubOrderRefund.class)
                .eq(TbBusinessSubOrderRefund::getSubOrderCode, refundCode));
        if (ObjectUtils.isEmpty(refundOrder)) {
            log.error("处理退款回调异常，需要人工干预。退款单号：{} 状态：{}", refundCode, status);
            throw new BusinessException(BusinessEnum.ORDER_PAY_CALLBACK_ERROR);
        }
        String orderStatus = null;
        String refundOrderStatus = null;
        String transactionInfoOrderStatus = null;
        // 退款成功，修改退款订单状态，关联的业务数据表中的订单状态也要修改
        if (DropdownEnum.SF_ROLLBACK_STATUS_SUCCESS.getDictKey().equals(status)) {
            orderStatus = DropdownEnum.ORDER_STATUS_REFUND_ALL.getDictKey();
            refundOrderStatus = DropdownEnum.ORDER_STATUS_REFUND_ALL.getDictKey();
            transactionInfoOrderStatus = DropdownEnum.TRANSACTION_ORDER_STATUS_REFUNDED.getDictKey();
        } else if (DropdownEnum.SF_ROLLBACK_STATUS_FAIL.getDictKey().equals(status)) {
            orderStatus = DropdownEnum.ORDER_STATUS_REFUND_FAIL.getDictKey();
            refundOrderStatus = DropdownEnum.ORDER_STATUS_REFUND_FAIL.getDictKey();
            transactionInfoOrderStatus = DropdownEnum.TRANSACTION_ORDER_STATUS_REFUNDED_FAIL.getDictKey();
        } else if (DropdownEnum.SF_ROLLBACK_STATUS_REJECT.getDictKey().equals(status)) {
            orderStatus = DropdownEnum.ORDER_STATUS_REFUND_REJECTED.getDictKey();
            refundOrderStatus = DropdownEnum.ORDER_STATUS_REFUND_REJECTED.getDictKey();
            transactionInfoOrderStatus= DropdownEnum.TRANSACTION_ORDER_STATUS_REFUNDED_REJECTED.getDictKey();
        }
        orderMapper.update(Wrappers.lambdaUpdate(TbBusinessOrder.class)
                .eq(TbBusinessOrder::getId, refundOrder.getParentOrderId())
                .set(TbBusinessOrder::getStatus, orderStatus));
        refundMapper.update(Wrappers.lambdaUpdate(TbBusinessSubOrderRefund.class)
                .eq(TbBusinessSubOrderRefund::getSubOrderCode, refundCode)
                .set(TbBusinessSubOrderRefund::getOrderStatus, refundOrderStatus));

        // 只有 意向金、定金 会存在退款 故这里不再做业务数据判断
        transactionInfoMapper.update(Wrappers.lambdaUpdate(TbBusinessTransactionInfo.class)
                .eq(TbBusinessTransactionInfo::getId, refundOrder.getBusinessId())
                .set(TbBusinessTransactionInfo::getOrderStatus, transactionInfoOrderStatus)
                .set(DropdownEnum.SF_ROLLBACK_STATUS_SUCCESS.getDictKey().equals(status), TbBusinessTransactionInfo::getTransactionDateTime, refundFinishTime)
        );
    }

    @Override
    public List<ReceiveCommunityVo> getReceiveCommunity(String communityCode, Boolean readCache, String transactionContractNum, Integer type) {
        if (ObjectUtils.isEmpty(communityCode)) {
            throw new BusinessException(BusinessEnum.COMMUNITY_CODE_NOT_NULL);
        }
        TbBusinessContract tbBizContract = contractMapper.selectOne(Wrappers.lambdaQuery(TbBusinessContract.class)
                .eq(!ObjectUtils.isEmpty(transactionContractNum), TbBusinessContract::getContractNumber, transactionContractNum)
                .or()
                .eq(TbBusinessContract::getId, 0));
        Map<String, String> contractFeeTypeMap = new LinkedHashMap<>();
        if (!ObjectUtils.isEmpty(tbBizContract)) {
            contractFeeTypeMap.putAll(signMappingMapper.selectList(Wrappers.lambdaQuery(TbBusinessContractSignMapping.class)
                            .eq(TbBusinessContractSignMapping::getContractType, tbBizContract.getContractType()))
                    .stream()
                    .filter(i -> !ObjectUtils.isEmpty(i.getTransactionFeeTypeCode()))
                    .collect(Collectors.toMap(TbBusinessContractSignMapping::getTransactionFeeTypeCode, TbBusinessContractSignMapping::getTransactionFeeType, (existing, next) -> existing)));
        }
        log.info("合同类型费项映射表：{}", JacksonUtil.toJSON(contractFeeTypeMap));
        SFOrderItemParam param = new SFOrderItemParam();
        param.setCommunityMdmCode(communityCode);
        param.setType(type);
        // 调用租售接口
        log.info("调用收费系统查询 收费项: {}", JacksonUtil.toJSON(param));
        SFOrderItemResp items = thirdServerClient.getItems(param);
        log.info("调用收费系统查询 收费项 返回 ：{}", JacksonUtil.toJSON(items));
        if (ObjectUtils.isEmpty(items)) {
            throw new BusinessException(CommonEnum.SYSTEM_ERROR);
        }
        if (!"0".equals(items.getCode())) {
            throw new BusinessException(items.getMessage(), CommonEnum.OTHER_ERROR.getCode());
        }
        Map<String, SFItemResp> itemRespMap = items.getContent()
                .stream()
                .map(SFOrderItemContentResp::getItems)
                .flatMap(List::stream)
                .collect(Collectors.toMap(SFItemResp::getItemCode, Function.identity(), (existing, next) -> existing));
        return dataServerClient.queryBySfCostItemCode(itemRespMap.keySet().stream().toList())
                .stream()
                .filter(costItemInfoResp -> {
                    return ObjectUtils.isEmpty(tbBizContract) || contractFeeTypeMap.containsKey(costItemInfoResp.getCostItemCode());
                })
                .map(costItemInfoResp -> {
                    String sfCostItemCode = costItemInfoResp.getSfCostItemCode();
                    SFItemResp sfItemResp = itemRespMap.get(sfCostItemCode);
                    ReceiveCommunityVo receiveCommunityVo = new ReceiveCommunityVo();
                    receiveCommunityVo.setItemId(sfItemResp.getItemId().toString());
                    receiveCommunityVo.setItemCode(costItemInfoResp.getSfCostItemCode());
                    receiveCommunityVo.setItemName(costItemInfoResp.getCostItemName());
                    receiveCommunityVo.setSfItemName(costItemInfoResp.getSfCostItemName());
                    receiveCommunityVo.setZsItemCode(costItemInfoResp.getCostItemCode());
                    log.info("收费项对象信息：{}", JacksonUtil.toJSON(receiveCommunityVo));
                    return receiveCommunityVo;
                }).toList();
    }



}
