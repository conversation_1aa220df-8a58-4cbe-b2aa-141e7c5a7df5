package mixc.be.rsms.business.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mixc.be.rsms.business.domain.pojo.TbDataConfCommissionTypeSnap;
import mixc.be.rsms.business.mapper.TbDataConfCommissionTypeSnapMapper;
import mixc.be.rsms.business.service.ICommissionTypeSnapService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 财务管理-提成配置配置类型(快照) 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor_ = @__(@Autowired))
@RefreshScope
public class CommissionTypeSnapServiceImpl extends ServiceImpl<TbDataConfCommissionTypeSnapMapper, TbDataConfCommissionTypeSnap> implements ICommissionTypeSnapService {

}
