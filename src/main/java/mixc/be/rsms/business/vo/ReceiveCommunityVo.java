package mixc.be.rsms.business.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2024/12/25
 */
@Data
public class ReceiveCommunityVo implements Serializable {
    @Serial
    private static final long serialVersionUID = -7179814019209373919L;

    /**
     * 收费项ID
     */
    @Schema(description = "收费系统-收费项ID")
    private String itemId;
    /**
     * 收费项code
     */
    @Schema(description = "收费系统-收费项code")
    private String itemCode;

    /**
     * 收费系统-收费项名称
     */
    @Schema(description = "收费系统-收费项名称")
    private String sfItemName;

    /**
     * 收费项名称
     */
    @Schema(description = "租售系统-收费项name")
    private String itemName;

    /**
     * 租售系统收费项code
     */
    @Schema(description = "租售系统-收费项code")
    private String zsItemCode;
}
