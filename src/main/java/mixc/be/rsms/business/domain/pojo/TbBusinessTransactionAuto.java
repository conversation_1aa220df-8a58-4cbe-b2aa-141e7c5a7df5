package mixc.be.rsms.business.domain.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * <p>
 * 成交报告-业绩分配信息-自动分配
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Data
@TableName("tb_business_transaction_auto")
@EqualsAndHashCode(callSuper = true)
public class TbBusinessTransactionAuto extends BaseEntity {

    @Serial
    private static final long serialVersionUID = -100501101549484945L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 成交报告id
     */
    @TableField("transaction_id")
    private Long transactionId;

    /**
     * 分成角色编码(house_input:房源录入人,house_maintainer:房源维护人,house_opener:房源开盘人,house_pic:房源图片人,house_video:房源视频人,house_key:房源钥匙人,house_delegate:房源委托人,house_bargain:房源议价人,client_first_input:客源首录人,client_maintainer:客源维护人,deal_maker:成交人,reserve:预留)
     */
    @TableField("tenths_role")
    private String tenthsRole;

    /**
     * 分成比例
     */
    @TableField("tenths_ratio")
    private BigDecimal tenthsRatio;

    /**
     * 固定金额
     */
    @TableField("tenths_money")
    private BigDecimal tenthsMoney;

    /**
     * 分成转移编码(house_input:房源录入人,house_maintainer:房源维护人,house_opener:房源开盘人,client_first_input:客源首录人,client_maintainer:客源维护人,deal_maker:成交人)
     */
    @TableField("tenths_transfer")
    private String tenthsTransfer;

    /**
     * 分成方式编码(percentage:比例分成,fixed_amount:固定金额分成)
     */
    @TableField("percentage")
    private String percentage;

    /**
     * 角色人id
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 金额
     */
    @TableField("amount")
    private BigDecimal amount;
}
