package mixc.be.rsms.business.domain.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * <p>
 * 成交报告-业绩分配信息-手动分配
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Data
@TableName("tb_business_transaction_manual")
@EqualsAndHashCode(callSuper = true)
public class TbBusinessTransactionManual extends BaseEntity {

    @Serial
    private static final long serialVersionUID = -1894961765121757243L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 成交报告id
     */
    @TableField("transaction_id")
    private Long transactionId;

    /**
     * 归属人id
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 业绩-performance，提成-commitment
     */
    @TableField("type")
    private String type;

    /**
     * 比例
     */
    @TableField("ratio")
    private BigDecimal ratio;

    /**
     * 金额
     */
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 分成方式编码(percentage:比例分配,fixed_amount:金额分配)
     */
    @TableField("percentage")
    private String percentage;
}
