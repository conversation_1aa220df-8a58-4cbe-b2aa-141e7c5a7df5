package mixc.be.rsms.business.domain.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 收佣计划表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tb_business_receive_commission_plan")
public class TbBusinessReceiveCommissionPlan extends BaseEntity {

    @Serial
    private static final long serialVersionUID = -280958378689063372L;
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 成交报告表ID
     */
    @TableField("report_id")
    private Long reportId;

    /**
     * 收费系统 费用项目Id
     */
    @TableField("cost_item_id")
    private String costItemId;

    /**
     * 租售系统收费项 code
     */
    @TableField("cost_item_code")
    private String costItemCode;


    /**
     * 租售系统收费项 desc
     */
    @TableField("cost_item_desc")
    private String costItemDesc;

    /**
     * 交费人：（owner:业主/customer:客户/other:第三方）
     */
    @TableField("payer")
    private String payer;

    /**
     * 应收佣金
     */
    @TableField("receivable_commission")
    private BigDecimal receivableCommission;


    /**
     * 预期交费时间
     */
    @TableField("expect_payment")
    private LocalDate expectPayment;

    /**
     * 实际缴费时间
     */
    @TableField("actual_payment")
    private LocalDateTime actualPayment;

    /**
     * 实收金额
     */
    @TableField("received_commission")
    private BigDecimal receivedCommission;


    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 意向金转佣 对应的意向金表ID
     */
    @TableField("deposit_id")
    private Long depositId;

    /**
     * 主订单Id
     */
    @TableField("order_id")
    private Long orderId;

    /**
     * 主订单编号
     */
    @TableField("order_code")
    private String orderCode;

    /**
     * 订单状态
     */
    @TableField("order_status")
    private String orderStatus;

    /**
     * 子订单ID
     */
    @TableField("sub_order_id")
    private Long subOrderId;

    /**
     * 子订单编号
     */
    @TableField("sub_order_code")
    private String subOrderCode;
}
