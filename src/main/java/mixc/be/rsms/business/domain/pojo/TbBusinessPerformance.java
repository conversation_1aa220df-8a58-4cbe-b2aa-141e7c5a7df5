package mixc.be.rsms.business.domain.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 业绩主表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Data
@TableName("tb_business_performance")
@EqualsAndHashCode(callSuper = true)
public class TbBusinessPerformance extends BaseEntity {

    @Serial
    private static final long serialVersionUID = -5579786684419253560L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 成交报告id
     */
    @TableField("transaction_id")
    private Long transactionId;

    /**
     * 业绩编码
     */
    @TableField("performance_code")
    private String performanceCode;

    /**
     * 角色人id/归属人id
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 角色人/归属人当前所在门店id
     */
    @TableField("store_id")
    private Long storeId;

    /**
     * 业绩期间
     */
    @TableField("performance_period")
    private String performancePeriod;

    /**
     * 业绩类型
     */
    @TableField("performance_type")
    private String performanceType;

    /**
     * 业绩阶段
     */
    @TableField("performance_stage")
    private String performanceStage;

    /**
     * 金额
     */
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 不含税金额
     */
    @TableField("excluding_tax_amount")
    private BigDecimal excludingTaxAmount;

    /**
     * 业绩日期
     */
    @TableField("performance_time")
    private LocalDateTime performanceTime;
}
