package mixc.be.rsms.business.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import mixc.be.rsms.business.domain.pojo.TbBusinessTransactionAuto;
import mixc.be.rsms.business.service.ITransactionAutoService;
import mixc.be.rsms.business.utils.ResultVoUtil;
import mixc.be.rsms.business.vo.ResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 成交报告-业绩分配信息-自动分配 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@RestController
@RequestMapping("/transactionAuto")
@Tag(name = "成交报告-业绩分配信息-自动分配")
@RequiredArgsConstructor(onConstructor_ = @__(@Autowired))
public class TransactionAutoController {

    private final ITransactionAutoService transactionAutoService;

    @Operation(summary = "分页查询")
    @GetMapping("/page")
    public ResultVo<IPage<TbBusinessTransactionAuto>> page(@RequestParam(defaultValue = "1") Integer pageNum,
                                                           @RequestParam(defaultValue = "10") Integer pageSize) {
        Page<TbBusinessTransactionAuto> page = new Page<>(pageNum, pageSize);
        return ResultVoUtil.success(transactionAutoService.page(page));
    }

    @Operation(summary = "根据ID查询")
    @GetMapping("/{id}")
    public ResultVo<TbBusinessTransactionAuto> getById(@PathVariable Long id) {
        return ResultVoUtil.success(transactionAutoService.getById(id));
    }

    @Operation(summary = "新增")
    @PostMapping
    public ResultVo<Boolean> save(@Valid @RequestBody TbBusinessTransactionAuto transactionAuto) {
        return ResultVoUtil.success(transactionAutoService.save(transactionAuto));
    }

    @Operation(summary = "修改")
    @PutMapping
    public ResultVo<Boolean> updateById(@Valid @RequestBody TbBusinessTransactionAuto transactionAuto) {
        return ResultVoUtil.success(transactionAutoService.updateById(transactionAuto));
    }

    @Operation(summary = "删除")
    @DeleteMapping("/{id}")
    public ResultVo<Boolean> removeById(@PathVariable Long id) {
        return ResultVoUtil.success(transactionAutoService.removeById(id));
    }

    @Operation(summary = "批量删除")
    @DeleteMapping("/batch")
    public ResultVo<Boolean> removeByIds(@RequestBody List<Long> ids) {
        return ResultVoUtil.success(transactionAutoService.removeByIds(ids));
    }

    @Operation(summary = "根据成交报告ID查询")
    @GetMapping("/transaction/{transactionId}")
    public ResultVo<List<TbBusinessTransactionAuto>> getByTransactionId(@PathVariable Long transactionId) {
        return ResultVoUtil.success(transactionAutoService.lambdaQuery()
                .eq(TbBusinessTransactionAuto::getTransactionId, transactionId)
                .list());
    }
}
