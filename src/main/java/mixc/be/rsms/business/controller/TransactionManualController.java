package mixc.be.rsms.business.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import mixc.be.rsms.business.domain.pojo.TbBusinessTransactionManual;
import mixc.be.rsms.business.service.ITransactionManualService;
import mixc.be.rsms.business.utils.ResultVoUtil;
import mixc.be.rsms.business.vo.ResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 成交报告-业绩分配信息-手动分配 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@RestController
@RequestMapping("/transactionManual")
@Tag(name = "成交报告-业绩分配信息-手动分配")
@RequiredArgsConstructor(onConstructor_ = @__(@Autowired))
public class TransactionManualController {

    private final ITransactionManualService transactionManualService;

    @Operation(summary = "分页查询")
    @GetMapping("/page")
    public ResultVo<IPage<TbBusinessTransactionManual>> page(@RequestParam(defaultValue = "1") Integer pageNum,
                                                             @RequestParam(defaultValue = "10") Integer pageSize) {
        Page<TbBusinessTransactionManual> page = new Page<>(pageNum, pageSize);
        return ResultVoUtil.success(transactionManualService.page(page));
    }

    @Operation(summary = "根据ID查询")
    @GetMapping("/{id}")
    public ResultVo<TbBusinessTransactionManual> getById(@PathVariable Long id) {
        return ResultVoUtil.success(transactionManualService.getById(id));
    }

    @Operation(summary = "新增")
    @PostMapping
    public ResultVo<Boolean> save(@Valid @RequestBody TbBusinessTransactionManual transactionManual) {
        return ResultVoUtil.success(transactionManualService.save(transactionManual));
    }

    @Operation(summary = "修改")
    @PutMapping
    public ResultVo<Boolean> updateById(@Valid @RequestBody TbBusinessTransactionManual transactionManual) {
        return ResultVoUtil.success(transactionManualService.updateById(transactionManual));
    }

    @Operation(summary = "删除")
    @DeleteMapping("/{id}")
    public ResultVo<Boolean> removeById(@PathVariable Long id) {
        return ResultVoUtil.success(transactionManualService.removeById(id));
    }

    @Operation(summary = "批量删除")
    @DeleteMapping("/batch")
    public ResultVo<Boolean> removeByIds(@RequestBody List<Long> ids) {
        return ResultVoUtil.success(transactionManualService.removeByIds(ids));
    }

    @Operation(summary = "根据成交报告ID查询")
    @GetMapping("/transaction/{transactionId}")
    public ResultVo<List<TbBusinessTransactionManual>> getByTransactionId(@PathVariable Long transactionId) {
        return ResultVoUtil.success(transactionManualService.lambdaQuery()
                .eq(TbBusinessTransactionManual::getTransactionId, transactionId)
                .list());
    }
}
