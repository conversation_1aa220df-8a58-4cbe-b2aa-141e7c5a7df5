package mixc.be.rsms.business.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import mixc.be.rsms.business.domain.pojo.TbDataConfCommissionTypeSnap;
import mixc.be.rsms.business.service.ICommissionTypeSnapService;
import mixc.be.rsms.business.utils.ResultVoUtil;
import mixc.be.rsms.business.vo.ResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 财务管理-提成配置配置类型(快照) 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@RestController
@RequestMapping("/commissionTypeSnap")
@Tag(name = "财务管理-提成配置配置类型(快照)")
@RequiredArgsConstructor(onConstructor_ = @__(@Autowired))
public class CommissionTypeSnapController {

    private final ICommissionTypeSnapService commissionTypeSnapService;

    @Operation(summary = "分页查询")
    @GetMapping("/page")
    public ResultVo<IPage<TbDataConfCommissionTypeSnap>> page(@RequestParam(defaultValue = "1") Integer pageNum,
                                                              @RequestParam(defaultValue = "10") Integer pageSize) {
        Page<TbDataConfCommissionTypeSnap> page = new Page<>(pageNum, pageSize);
        return ResultVoUtil.success(commissionTypeSnapService.page(page));
    }

    @Operation(summary = "根据ID查询")
    @GetMapping("/{id}")
    public ResultVo<TbDataConfCommissionTypeSnap> getById(@PathVariable Long id) {
        return ResultVoUtil.success(commissionTypeSnapService.getById(id));
    }

    @Operation(summary = "新增")
    @PostMapping
    public ResultVo<Boolean> save(@Valid @RequestBody TbDataConfCommissionTypeSnap commissionTypeSnap) {
        return ResultVoUtil.success(commissionTypeSnapService.save(commissionTypeSnap));
    }

    @Operation(summary = "修改")
    @PutMapping
    public ResultVo<Boolean> updateById(@Valid @RequestBody TbDataConfCommissionTypeSnap commissionTypeSnap) {
        return ResultVoUtil.success(commissionTypeSnapService.updateById(commissionTypeSnap));
    }

    @Operation(summary = "删除")
    @DeleteMapping("/{id}")
    public ResultVo<Boolean> removeById(@PathVariable Long id) {
        return ResultVoUtil.success(commissionTypeSnapService.removeById(id));
    }

    @Operation(summary = "批量删除")
    @DeleteMapping("/batch")
    public ResultVo<Boolean> removeByIds(@RequestBody List<Long> ids) {
        return ResultVoUtil.success(commissionTypeSnapService.removeByIds(ids));
    }

    @Operation(summary = "根据提成明细项ID查询")
    @GetMapping("/commitmentItem/{commitmentItemId}")
    public ResultVo<List<TbDataConfCommissionTypeSnap>> getByCommitmentItemId(@PathVariable Long commitmentItemId) {
        return ResultVoUtil.success(commissionTypeSnapService.lambdaQuery()
                .eq(TbDataConfCommissionTypeSnap::getCommitmentItemId, commitmentItemId)
                .list());
    }

    @Operation(summary = "根据配置类型查询")
    @GetMapping("/configType/{configType}")
    public ResultVo<List<TbDataConfCommissionTypeSnap>> getByConfigType(@PathVariable String configType) {
        return ResultVoUtil.success(commissionTypeSnapService.lambdaQuery()
                .eq(TbDataConfCommissionTypeSnap::getConfigType, configType)
                .list());
    }
}
