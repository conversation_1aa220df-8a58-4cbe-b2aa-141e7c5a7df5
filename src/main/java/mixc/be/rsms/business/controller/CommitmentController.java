package mixc.be.rsms.business.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import mixc.be.rsms.business.domain.pojo.TbBusinessCommitment;
import mixc.be.rsms.business.service.ICommitmentService;
import mixc.be.rsms.business.utils.ResultVoUtil;
import mixc.be.rsms.business.vo.ResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 提成主表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@RestController
@RequestMapping("/commitment")
@Tag(name = "提成主表")
@RequiredArgsConstructor(onConstructor_ = @__(@Autowired))
public class CommitmentController {

    private final ICommitmentService commitmentService;

    @Operation(summary = "分页查询")
    @GetMapping("/page")
    public ResultVo<IPage<TbBusinessCommitment>> page(@RequestParam(defaultValue = "1") Integer pageNum,
                                                      @RequestParam(defaultValue = "10") Integer pageSize) {
        Page<TbBusinessCommitment> page = new Page<>(pageNum, pageSize);
        return ResultVoUtil.success(commitmentService.page(page));
    }

    @Operation(summary = "根据ID查询")
    @GetMapping("/{id}")
    public ResultVo<TbBusinessCommitment> getById(@PathVariable Long id) {
        return ResultVoUtil.success(commitmentService.getById(id));
    }

    @Operation(summary = "新增")
    @PostMapping
    public ResultVo<Boolean> save(@Valid @RequestBody TbBusinessCommitment commitment) {
        return ResultVoUtil.success(commitmentService.save(commitment));
    }

    @Operation(summary = "修改")
    @PutMapping
    public ResultVo<Boolean> updateById(@Valid @RequestBody TbBusinessCommitment commitment) {
        return ResultVoUtil.success(commitmentService.updateById(commitment));
    }

    @Operation(summary = "删除")
    @DeleteMapping("/{id}")
    public ResultVo<Boolean> removeById(@PathVariable Long id) {
        return ResultVoUtil.success(commitmentService.removeById(id));
    }

    @Operation(summary = "批量删除")
    @DeleteMapping("/batch")
    public ResultVo<Boolean> removeByIds(@RequestBody List<Long> ids) {
        return ResultVoUtil.success(commitmentService.removeByIds(ids));
    }

    @Operation(summary = "根据提成编码查询")
    @GetMapping("/code/{commitmentCode}")
    public ResultVo<TbBusinessCommitment> getByCommitmentCode(@PathVariable String commitmentCode) {
        return ResultVoUtil.success(commitmentService.lambdaQuery()
                .eq(TbBusinessCommitment::getCommitmentCode, commitmentCode)
                .one());
    }

    @Operation(summary = "根据发放状态查询")
    @GetMapping("/status/{distributionStatus}")
    public ResultVo<List<TbBusinessCommitment>> getByDistributionStatus(@PathVariable Integer distributionStatus) {
        return ResultVoUtil.success(commitmentService.lambdaQuery()
                .eq(TbBusinessCommitment::getDistributionStatus, distributionStatus)
                .list());
    }
}
