package mixc.be.rsms.business.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import mixc.be.rsms.business.domain.pojo.TbBusinessPerformance;
import mixc.be.rsms.business.service.IPerformanceService;
import mixc.be.rsms.business.utils.ResultVoUtil;
import mixc.be.rsms.business.vo.ResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 业绩主表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@RestController
@RequestMapping("/performance")
@Tag(name = "业绩主表")
@RequiredArgsConstructor(onConstructor_ = @__(@Autowired))
public class PerformanceController {

    private final IPerformanceService performanceService;

    @Operation(summary = "分页查询")
    @GetMapping("/page")
    public ResultVo<IPage<TbBusinessPerformance>> page(@RequestParam(defaultValue = "1") Integer pageNum,
                                                       @RequestParam(defaultValue = "10") Integer pageSize) {
        Page<TbBusinessPerformance> page = new Page<>(pageNum, pageSize);
        return ResultVoUtil.success(performanceService.page(page));
    }

    @Operation(summary = "根据ID查询")
    @GetMapping("/{id}")
    public ResultVo<TbBusinessPerformance> getById(@PathVariable Long id) {
        return ResultVoUtil.success(performanceService.getById(id));
    }

    @Operation(summary = "新增")
    @PostMapping
    public ResultVo<Boolean> save(@Valid @RequestBody TbBusinessPerformance performance) {
        return ResultVoUtil.success(performanceService.save(performance));
    }

    @Operation(summary = "修改")
    @PutMapping
    public ResultVo<Boolean> updateById(@Valid @RequestBody TbBusinessPerformance performance) {
        return ResultVoUtil.success(performanceService.updateById(performance));
    }

    @Operation(summary = "删除")
    @DeleteMapping("/{id}")
    public ResultVo<Boolean> removeById(@PathVariable Long id) {
        return ResultVoUtil.success(performanceService.removeById(id));
    }

    @Operation(summary = "批量删除")
    @DeleteMapping("/batch")
    public ResultVo<Boolean> removeByIds(@RequestBody List<Long> ids) {
        return ResultVoUtil.success(performanceService.removeByIds(ids));
    }

    @Operation(summary = "根据成交报告ID查询")
    @GetMapping("/transaction/{transactionId}")
    public ResultVo<List<TbBusinessPerformance>> getByTransactionId(@PathVariable Long transactionId) {
        return ResultVoUtil.success(performanceService.lambdaQuery()
                .eq(TbBusinessPerformance::getTransactionId, transactionId)
                .list());
    }

    @Operation(summary = "根据用户ID查询")
    @GetMapping("/user/{userId}")
    public ResultVo<List<TbBusinessPerformance>> getByUserId(@PathVariable Long userId) {
        return ResultVoUtil.success(performanceService.lambdaQuery()
                .eq(TbBusinessPerformance::getUserId, userId)
                .list());
    }
}
