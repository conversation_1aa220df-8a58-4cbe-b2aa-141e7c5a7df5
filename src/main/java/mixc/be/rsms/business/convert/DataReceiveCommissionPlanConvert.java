package mixc.be.rsms.business.convert;

import mixc.be.rsms.business.domain.dto.ReceiveCommissionPlanAddParams;
import mixc.be.rsms.business.domain.pojo.TbBusinessReceiveCommissionPlan;
import mixc.be.rsms.business.vo.ReceiveCommissionPlanVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @description
 * @date 2024/10/22
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DataReceiveCommissionPlanConvert {

    @Mappings({
            @Mapping(target = "costItemDesc", source =  "costItemDesc", ignore = true)
    })
    TbBusinessReceiveCommissionPlan addParamsToData(ReceiveCommissionPlanAddParams params);

    @Mappings({
            @Mapping(target = "costItemDesc", source = "costItemDesc", ignore = true)
    })
    ReceiveCommissionPlanVo dataToVo(TbBusinessReceiveCommissionPlan data);
}
